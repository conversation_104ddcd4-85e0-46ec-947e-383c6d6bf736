"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/chat/chat-interface.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/chat-interface.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_chat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/chat */ \"(app-pages-browser)/./src/services/chat.ts\");\n/* harmony import */ var _services_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/character */ \"(app-pages-browser)/./src/services/character.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/id.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst MessageContent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { content, role } = param;\n    if (role === 'user') {\n        // For user messages, just display as plain text with line breaks\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm whitespace-pre-wrap leading-relaxed\",\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 35,\n            columnNumber: 12\n        }, undefined);\n    }\n    // For AI messages, render as Markdown\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-sm leading-relaxed prose prose-sm max-w-none dark:prose-invert\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_9__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n            ],\n            components: {\n                // Customize paragraph styling\n                p: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-2 last:mb-0 leading-relaxed\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize emphasis (italic)\n                em: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        className: \"italic text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize strong (bold)\n                strong: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        className: \"font-semibold text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize code blocks\n                code: (param)=>{\n                    let { children, className } = param;\n                    const isInline = !className;\n                    if (isInline) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"bg-muted px-1.5 py-0.5 rounded text-xs font-mono\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 17\n                        }, void 0);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"block bg-muted p-3 rounded-lg text-xs font-mono overflow-x-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // Customize lists\n                ul: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                ol: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                li: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"text-sm\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize blockquotes\n                blockquote: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-muted pl-4 italic my-2\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 13\n                    }, void 0);\n                }\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, undefined);\n});\n_c = MessageContent;\nconst MessageItem = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s((param)=>{\n    let { message, characterImage, characterName } = param;\n    var _characterName_charAt;\n    _s();\n    const formatTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageItem.useCallback[formatTime]\": (dateString)=>{\n            try {\n                return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_11__.formatDistanceToNow)(new Date(dateString), {\n                    addSuffix: true,\n                    locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_12__.id\n                });\n            } catch (e) {\n                return '';\n            }\n        }\n    }[\"MessageItem.useCallback[formatTime]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n        children: [\n            message.role === 'assistant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                className: \"w-8 h-8 mb-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                        src: characterImage,\n                        alt: characterName\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                        className: \"text-xs bg-bestieku-primary/10 text-bestieku-primary\",\n                        children: (characterName === null || characterName === void 0 ? void 0 : (_characterName_charAt = characterName.charAt(0)) === null || _characterName_charAt === void 0 ? void 0 : _characterName_charAt.toUpperCase()) || 'C'\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl p-4 shadow-sm \".concat(message.role === 'user' ? 'bg-bestieku-primary text-white rounded-br-md' : 'bg-white dark:bg-muted border rounded-bl-md'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: message.content,\n                        role: message.role\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mt-2 \".concat(message.role === 'user' ? 'text-white/70' : 'text-muted-foreground'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: formatTime(message.createdAt)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, undefined),\n                            message.role === 'user' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, undefined);\n}, \"HvJHf3nLs/NeKKNMBW1x3UPtyJE=\"));\n_c1 = MessageItem;\nconst StreamingMessage = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { streamingMessage, characterImage, characterName } = param;\n    var _characterName_charAt;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 justify-start\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                className: \"w-8 h-8 mb-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                        src: characterImage,\n                        alt: characterName\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                        className: \"text-xs bg-bestieku-primary/10 text-bestieku-primary\",\n                        children: (characterName === null || characterName === void 0 ? void 0 : (_characterName_charAt = characterName.charAt(0)) === null || _characterName_charAt === void 0 ? void 0 : _characterName_charAt.toUpperCase()) || 'C'\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl rounded-bl-md p-4 bg-white dark:bg-muted border shadow-sm\",\n                children: [\n                    streamingMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: streamingMessage,\n                        role: \"assistant\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"Berpikir\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 ml-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-bestieku-primary rounded-full animate-bounce\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-bestieku-primary rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '0.1s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-bestieku-primary rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '0.2s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-2 text-muted-foreground\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 h-1 bg-bestieku-primary rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: \"Mengetik...\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, undefined);\n});\n_c2 = StreamingMessage;\nconst WelcomeMessage = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { character, messageCount } = param;\n    var _character_name_charAt, _character_name;\n    if (!character) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center py-8 px-4 space-y-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                            className: \"w-20 h-20 mx-auto ring-4 ring-bestieku-primary/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                                    src: character.image,\n                                    alt: character.name\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                                    className: \"bg-bestieku-primary/10 text-bestieku-primary font-semibold text-2xl\",\n                                    children: ((_character_name = character.name) === null || _character_name === void 0 ? void 0 : (_character_name_charAt = _character_name.charAt(0)) === null || _character_name_charAt === void 0 ? void 0 : _character_name_charAt.toUpperCase()) || 'C'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-1 right-1/2 transform translate-x-6 w-4 h-4 bg-green-500 border-2 border-background rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold mb-2\",\n                    children: character.name\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, undefined),\n                character.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground mb-3 font-medium\",\n                    children: character.title\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center gap-2 mb-4\",\n                    children: [\n                        character.storyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                            className: \"bg-bestieku-primary text-white\",\n                            children: \"Story Mode\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                            variant: \"outline\",\n                            className: \"capitalize\",\n                            children: character.status\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center gap-4 text-sm text-muted-foreground\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                messageCount,\n                                \" pesan\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"•\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-500\",\n                            children: \"Online\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 203,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 201,\n        columnNumber: 5\n    }, undefined);\n});\n_c3 = WelcomeMessage;\nconst OpeningScene = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { character, hasMessages } = param;\n    var _character_name_charAt, _character_name;\n    if (!(character === null || character === void 0 ? void 0 : character.openingScene)) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"px-4 \".concat(hasMessages ? 'pb-4' : 'pt-4'),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3 max-w-4xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                    className: \"w-8 h-8 flex-shrink-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                            src: character.image,\n                            alt: character.name\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                            className: \"text-xs bg-bestieku-primary/10 text-bestieku-primary\",\n                            children: ((_character_name = character.name) === null || _character_name === void 0 ? void 0 : (_character_name_charAt = _character_name.charAt(0)) === null || _character_name_charAt === void 0 ? void 0 : _character_name_charAt.toUpperCase()) || 'C'\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-sm\",\n                                    children: character.name\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-muted-foreground\",\n                                    children: \"Adegan Pembuka\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-muted rounded-2xl rounded-tl-md p-4 shadow-sm border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm leading-relaxed\",\n                                children: character.openingScene\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 258,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 256,\n        columnNumber: 5\n    }, undefined);\n});\n_c4 = OpeningScene;\nconst ChatInput = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s1((param)=>{\n    let { onSendMessage, disabled, characterName, isStreaming } = param;\n    _s1();\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Use ref to store current message to avoid stale closures\n    const messageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)('');\n    messageRef.current = newMessage;\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleSendMessage]\": ()=>{\n            const currentMessage = messageRef.current.trim();\n            if (!currentMessage || disabled) return;\n            onSendMessage(currentMessage);\n            setNewMessage('');\n            messageRef.current = '';\n        }\n    }[\"ChatInput.useCallback[handleSendMessage]\"], [\n        disabled,\n        onSendMessage\n    ]);\n    const handleKeyPress = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleKeyPress]\": (e)=>{\n            if (e.key === 'Enter' && !e.shiftKey) {\n                e.preventDefault();\n                handleSendMessage();\n            }\n        }\n    }[\"ChatInput.useCallback[handleKeyPress]\"], [\n        handleSendMessage\n    ]);\n    const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleInputChange]\": (e)=>{\n            const value = e.target.value;\n            setNewMessage(value);\n            messageRef.current = value;\n        }\n    }[\"ChatInput.useCallback[handleInputChange]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-shrink-0 border-t p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-end space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                ref: inputRef,\n                                value: newMessage,\n                                onChange: handleInputChange,\n                                onKeyPress: handleKeyPress,\n                                placeholder: \"Message \".concat(characterName || 'character', \"...\"),\n                                disabled: disabled,\n                                className: \"pr-12 py-3 rounded-2xl border-2 focus:border-bestieku-primary transition-colors\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                children: disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-4 h-4 border-2 border-bestieku-primary border-t-transparent rounded-full animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: handleSendMessage,\n                        disabled: !newMessage.trim() || disabled,\n                        className: \"bg-bestieku-primary hover:bg-bestieku-primary-dark rounded-2xl px-6 py-3 transition-all duration-200 hover:scale-105 disabled:hover:scale-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mt-2 text-xs text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Tekan Enter untuk mengirim, Shift+Enter untuk baris baru\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 9\n                    }, undefined),\n                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-bestieku-primary animate-pulse\",\n                        children: \"AI sedang merespons...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 348,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 318,\n        columnNumber: 5\n    }, undefined);\n}, \"1o5B5G/jtGrG8z8Ow45dQHalZdE=\"));\n_c5 = ChatInput;\nconst ChatInterface = /*#__PURE__*/ _s2((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c6 = _s2((param)=>{\n    let { chat, onBack, onToggleProfile, onChatReset, selectedBackground } = param;\n    var _chat_character, _chat_character1, _chat_character2, _chat_character3, _chat_character4;\n    _s2();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessage, setStreamingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [characterAssets, setCharacterAssets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [backgroundAssetUrl, setBackgroundAssetUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [character, setCharacter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamConnectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamingTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Delete chat messages function\n    const handleDeleteMessages = async ()=>{\n        if (!chat.id) return;\n        try {\n            setIsDeleting(true);\n            await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.deleteChatMessages(chat.id);\n            // Clear messages locally\n            setMessages([]);\n            // Show success toast\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"Chat berhasil direset!\", {\n                description: \"Semua pesan telah dihapus. Anda bisa memulai percakapan baru.\",\n                duration: 4000\n            });\n            // Call parent callback if provided\n            if (onChatReset) {\n                onChatReset();\n            }\n        } catch (error) {\n            console.error('Failed to delete messages:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Gagal mereset chat\", {\n                description: \"Terjadi kesalahan saat menghapus pesan. Silakan coba lagi.\",\n                duration: 4000\n            });\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    // Stable references for character data to prevent unnecessary re-renders\n    const characterImageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)((_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image);\n    const characterNameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)((_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name);\n    // Load character data and assets\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const loadCharacterData = {\n                \"ChatInterface.useEffect.loadCharacterData\": async ()=>{\n                    try {\n                        // Load character details\n                        const characterData = await _services_character__WEBPACK_IMPORTED_MODULE_3__.characterService.getCharacterById(chat.characterId);\n                        setCharacter(characterData);\n                        // Load character assets\n                        const assets = await _services_character__WEBPACK_IMPORTED_MODULE_3__.characterService.getCharacterAssets(chat.characterId);\n                        setCharacterAssets(assets);\n                    } catch (error) {\n                        console.error('Failed to load character data:', error);\n                    }\n                }\n            }[\"ChatInterface.useEffect.loadCharacterData\"];\n            loadCharacterData();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        chat.characterId\n    ]);\n    // Update background URL when selectedBackground changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (selectedBackground && characterAssets.length > 0) {\n                const backgroundAsset = characterAssets.find({\n                    \"ChatInterface.useEffect.backgroundAsset\": (asset)=>asset.id === selectedBackground && asset.purpose === 'background'\n                }[\"ChatInterface.useEffect.backgroundAsset\"]);\n                setBackgroundAssetUrl((backgroundAsset === null || backgroundAsset === void 0 ? void 0 : backgroundAsset.url) || null);\n            } else {\n                setBackgroundAssetUrl(null);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        selectedBackground,\n        characterAssets\n    ]);\n    // Detect mobile screen size\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkMobile = {\n                \"ChatInterface.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"ChatInterface.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"ChatInterface.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    // Update refs when chat changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            var _chat_character, _chat_character1;\n            characterImageRef.current = (_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image;\n            characterNameRef.current = (_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        (_chat_character2 = chat.character) === null || _chat_character2 === void 0 ? void 0 : _chat_character2.image,\n        (_chat_character3 = chat.character) === null || _chat_character3 === void 0 ? void 0 : _chat_character3.name\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            loadMessages();\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    // Cleanup stream connection on unmount\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    // Cleanup streaming timeout\n                    if (streamingTimeoutRef.current) {\n                        clearTimeout(streamingTimeoutRef.current);\n                    }\n                    // Cleanup stream timeout\n                    if (streamTimeoutRef.current) {\n                        clearTimeout(streamTimeoutRef.current);\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        chat.id\n    ]);\n    // Stable scroll function that doesn't cause re-renders\n    const scrollToBottomRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        \"ChatInterface.useRef[scrollToBottomRef]\": ()=>{}\n    }[\"ChatInterface.useRef[scrollToBottomRef]\"]);\n    scrollToBottomRef.current = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    // Only scroll when messages count changes, not on every render\n    const messagesCountRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(messages.length);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > messagesCountRef.current) {\n                messagesCountRef.current = messages.length;\n                const timeoutId = setTimeout({\n                    \"ChatInterface.useEffect.timeoutId\": ()=>{\n                        var _scrollToBottomRef_current;\n                        (_scrollToBottomRef_current = scrollToBottomRef.current) === null || _scrollToBottomRef_current === void 0 ? void 0 : _scrollToBottomRef_current.call(scrollToBottomRef);\n                    }\n                }[\"ChatInterface.useEffect.timeoutId\"], 100);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"ChatInterface.useEffect\"];\n            }\n            messagesCountRef.current = messages.length;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    // Separate effect for streaming - only scroll when streaming starts or ends\n    const isStreamingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(isStreaming);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (isStreaming && !isStreamingRef.current) {\n                // Streaming just started\n                const timeoutId = setTimeout({\n                    \"ChatInterface.useEffect.timeoutId\": ()=>{\n                        var _scrollToBottomRef_current;\n                        (_scrollToBottomRef_current = scrollToBottomRef.current) === null || _scrollToBottomRef_current === void 0 ? void 0 : _scrollToBottomRef_current.call(scrollToBottomRef);\n                    }\n                }[\"ChatInterface.useEffect.timeoutId\"], 200);\n                isStreamingRef.current = isStreaming;\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"ChatInterface.useEffect\"];\n            }\n            isStreamingRef.current = isStreaming;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        isStreaming\n    ]);\n    const loadMessages = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.getChatMessages(chat.id, {\n                limit: 50\n            });\n            setMessages(response.data.reverse()); // Reverse to show oldest first\n        } catch (error) {\n            console.error('Failed to load messages:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInterface.useCallback[handleSendMessage]\": async (messageText)=>{\n            if (!messageText.trim() || sending) return;\n            const tempId = \"temp-\".concat(Date.now());\n            setSending(true);\n            try {\n                // Add user message to UI immediately\n                const userMessage = {\n                    id: tempId,\n                    role: 'user',\n                    content: messageText,\n                    contentType: 'text',\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                };\n                setMessages({\n                    \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>[\n                            ...prev,\n                            userMessage\n                        ]\n                }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                console.log('Sending message to chat:', chat.id, {\n                    message: messageText,\n                    streaming: true\n                });\n                // Send message with streaming enabled\n                const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.sendMessage(chat.id, {\n                    message: messageText,\n                    streaming: true\n                });\n                console.log('Message sent successfully:', response);\n                // Update the temporary message with real ID if available\n                if (response.id) {\n                    setMessages({\n                        \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.map({\n                                \"ChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id === tempId ? {\n                                        ...msg,\n                                        id: response.id\n                                    } : msg\n                            }[\"ChatInterface.useCallback[handleSendMessage]\"])\n                    }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                }\n                // Start streaming response\n                await startStreaming();\n            } catch (error) {\n                console.error('Failed to send message:', error);\n                // Remove the temporary user message on error\n                setMessages({\n                    \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.filter({\n                            \"ChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id !== tempId\n                        }[\"ChatInterface.useCallback[handleSendMessage]\"])\n                }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                alert('Failed to send message. Please try again.');\n            } finally{\n                setSending(false);\n            }\n        }\n    }[\"ChatInterface.useCallback[handleSendMessage]\"], [\n        sending,\n        chat.id\n    ]);\n    const startStreaming = async ()=>{\n        console.log('Starting stream for chat:', chat.id);\n        setIsStreaming(true);\n        setStreamingMessage('');\n        // Close existing connection and clear timeouts\n        if (streamConnectionRef.current) {\n            streamConnectionRef.current.close();\n        }\n        if (streamingTimeoutRef.current) {\n            clearTimeout(streamingTimeoutRef.current);\n        }\n        if (streamTimeoutRef.current) {\n            clearTimeout(streamTimeoutRef.current);\n        }\n        let currentStreamingMessage = '';\n        // More aggressive throttling for streaming message updates\n        let lastUpdateTime = 0;\n        const updateStreamingMessage = (message)=>{\n            const now = Date.now();\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n            }\n            // Immediate update if it's been more than 100ms since last update\n            if (now - lastUpdateTime > 100) {\n                setStreamingMessage(message);\n                lastUpdateTime = now;\n            } else {\n                // Otherwise, throttle the update\n                streamingTimeoutRef.current = setTimeout(()=>{\n                    setStreamingMessage(message);\n                    lastUpdateTime = Date.now();\n                }, 100);\n            }\n        };\n        const onMessage = (data)=>{\n            console.log('Received SSE event:', data);\n            switch(data.event){\n                case 'start':\n                    console.log('Stream started');\n                    currentStreamingMessage = '';\n                    setStreamingMessage('');\n                    break;\n                case 'token':\n                    currentStreamingMessage += data.data;\n                    updateStreamingMessage(currentStreamingMessage);\n                    break;\n                case 'metadata':\n                    console.log('Stream metadata:', data.data);\n                    break;\n                case 'end':\n                    var _data_data;\n                    console.log('Stream ended, final message:', currentStreamingMessage);\n                    // Clear any pending streaming updates\n                    if (streamingTimeoutRef.current) {\n                        clearTimeout(streamingTimeoutRef.current);\n                    }\n                    // Clear stream timeout to prevent false timeout errors\n                    if (streamTimeoutRef.current) {\n                        clearTimeout(streamTimeoutRef.current);\n                    }\n                    // Finalize the streaming message\n                    const finalMessage = {\n                        id: ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.chatMessageId) || \"msg-\".concat(Date.now()),\n                        role: 'assistant',\n                        content: currentStreamingMessage,\n                        contentType: 'text',\n                        createdAt: new Date().toISOString(),\n                        updatedAt: new Date().toISOString()\n                    };\n                    setMessages((prev)=>[\n                            ...prev,\n                            finalMessage\n                        ]);\n                    setStreamingMessage('');\n                    setIsStreaming(false);\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    break;\n                default:\n                    console.log('Unknown SSE event:', data.event);\n            }\n        };\n        const onError = async (error)=>{\n            var _error_message;\n            console.error('Stream Error:', error);\n            // Clear timeouts first to prevent further errors\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n            }\n            if (streamTimeoutRef.current) {\n                clearTimeout(streamTimeoutRef.current);\n            }\n            setIsStreaming(false);\n            setStreamingMessage('');\n            if (streamConnectionRef.current) {\n                streamConnectionRef.current.close();\n            }\n            // Only reload messages if it's not a timeout error and streaming was actually active\n            if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('timeout')) && isStreaming) {\n                console.log('Attempting to reload messages as fallback...');\n                try {\n                    await loadMessages();\n                } catch (reloadError) {\n                    console.error('Failed to reload messages:', reloadError);\n                // Don't show alert for timeout errors to avoid disrupting user\n                }\n            }\n        };\n        try {\n            // Create new stream connection\n            const connection = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.createStreamConnection(chat.id, onMessage, onError);\n            streamConnectionRef.current = connection;\n            // Set timeout for streaming (30 seconds) - but store reference to clear it\n            streamTimeoutRef.current = setTimeout(()=>{\n                // Only trigger timeout if streaming is still active and connection exists\n                if (isStreaming && streamConnectionRef.current) {\n                    console.log('Stream timeout, falling back to message reload');\n                    onError(new Error('Stream timeout'));\n                }\n            }, 30000);\n        } catch (error) {\n            console.error('Failed to create stream connection:', error);\n            setIsStreaming(false);\n            alert('Failed to establish connection for AI response. Please try again.');\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 h-20 border-b p-4 animate-pulse flex items-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-muted rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 712,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded w-32\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 714,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-muted rounded w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 715,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 713,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 711,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 710,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-4 space-y-4 overflow-y-auto\",\n                    children: Array.from({\n                        length: 3\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-muted rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 722,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 724,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 725,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 723,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, i, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 721,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 719,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 709,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 h-16 border-b p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 flex items-center justify-between w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            isMobile && onBack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: onBack,\n                                className: \"hover:bg-muted/50 md:hidden\",\n                                \"aria-label\": \"Back to chat list\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 748,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 741,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"font-semibold text-lg\",\n                                    children: ((_chat_character4 = chat.character) === null || _chat_character4 === void 0 ? void 0 : _chat_character4.name) || 'Chat'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 754,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 753,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 738,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            isMobile && onToggleProfile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: onToggleProfile,\n                                className: \"hover:bg-muted/50 md:hidden\",\n                                \"aria-label\": \"View character profile\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 768,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 761,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: handleDeleteMessages,\n                                disabled: isDeleting || messages.length === 0,\n                                className: \"text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 780,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    isDeleting ? 'Mereset...' : 'Reset'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 773,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 758,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 737,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto overflow-x-hidden relative\",\n                style: {\n                    background: backgroundAssetUrl ? \"linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.8)), url(\".concat(backgroundAssetUrl, \")\") : 'linear-gradient(to bottom, hsl(var(--background)), hsl(var(--muted))/0.2)',\n                    backgroundSize: backgroundAssetUrl ? 'cover' : 'auto',\n                    backgroundPosition: backgroundAssetUrl ? 'center' : 'auto',\n                    backgroundRepeat: backgroundAssetUrl ? 'no-repeat' : 'auto'\n                },\n                children: [\n                    messages.length === 0 && character && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WelcomeMessage, {\n                        character: character,\n                        messageCount: chat.messageCount\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 800,\n                        columnNumber: 11\n                    }, undefined),\n                    character && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OpeningScene, {\n                        character: character,\n                        hasMessages: messages.length > 0\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 808,\n                        columnNumber: 11\n                    }, undefined),\n                    messages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 pb-4 space-y-4\",\n                        children: [\n                            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageItem, {\n                                    message: message,\n                                    characterImage: characterImageRef.current,\n                                    characterName: characterNameRef.current\n                                }, message.id, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 815,\n                                    columnNumber: 15\n                                }, undefined)),\n                            isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamingMessage, {\n                                streamingMessage: streamingMessage,\n                                characterImage: characterImageRef.current,\n                                characterName: characterNameRef.current\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 825,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 813,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 834,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 787,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatInput, {\n                onSendMessage: handleSendMessage,\n                disabled: sending || isStreaming,\n                characterName: characterNameRef.current,\n                isStreaming: isStreaming\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 838,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 735,\n        columnNumber: 5\n    }, undefined);\n}, \"wFJ1SEqwOF5CTh2UO5d9ZjLHLFo=\")), \"wFJ1SEqwOF5CTh2UO5d9ZjLHLFo=\");\n_c7 = ChatInterface;\nChatInterface.displayName = 'ChatInterface';\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"MessageContent\");\n$RefreshReg$(_c1, \"MessageItem\");\n$RefreshReg$(_c2, \"StreamingMessage\");\n$RefreshReg$(_c3, \"WelcomeMessage\");\n$RefreshReg$(_c4, \"OpeningScene\");\n$RefreshReg$(_c5, \"ChatInput\");\n$RefreshReg$(_c6, \"ChatInterface$memo\");\n$RefreshReg$(_c7, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/chat-interface.tsx\n"));

/***/ })

});