'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Chat } from '@/types/chat';
import { chatService } from '@/services/chat';
import { characterService } from '@/services/character';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Search, MessageCircle, Loader2 } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { id } from 'date-fns/locale';

interface ChatListProps {
  selectedChatId?: string;
  onChatSelect: (chat: Chat) => void;
  onChatsLoaded?: (chats: Chat[]) => void;
}

export function ChatList({ selectedChatId, onChatSelect, onChatsLoaded }: ChatListProps) {
  const [chats, setChats] = useState<Chat[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMorePages, setHasMorePages] = useState(true);
  const [totalItems, setTotalItems] = useState(0);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const isLoadingRef = useRef(false);

  useEffect(() => {
    loadChats(1, true);
  }, []);

  const loadChats = async (page: number = 1, reset: boolean = false) => {
    // Prevent concurrent requests
    if (isLoadingRef.current) return;

    try {
      isLoadingRef.current = true;

      if (reset) {
        setLoading(true);
        setChats([]);
        setCurrentPage(1);
        setHasMorePages(true);
      } else {
        setLoadingMore(true);
      }

      const response = await chatService.getChats({
        page,
        limit: reset ? 50 : 10 // Load 50 items initially, then 10 per page for subsequent loads
      });

      // Enrich chats with character data
      const enrichedChats = await Promise.all(
        response.data.map(async (chat) => {
          try {
            const character = await characterService.getCharacterById(chat.characterId);
            return {
              ...chat,
              character: {
                id: character.id,
                name: character.name,
                image: character.image,
              },
            };
          } catch (error) {
            console.error(`Failed to load character ${chat.characterId}:`, error);
            return {
              ...chat,
              character: {
                id: chat.characterId,
                name: 'Unknown Character',
                image: '',
              },
            };
          }
        })
      );

      if (reset) {
        setChats(enrichedChats);
        // Notify parent that chats are loaded
        if (onChatsLoaded) {
          onChatsLoaded(enrichedChats);
        }
      } else {
        // Deduplicate chats by ID to prevent duplicate keys
        setChats(prev => {
          const existingIds = new Set(prev.map(chat => chat.id));
          const newChats = enrichedChats.filter(chat => !existingIds.has(chat.id));
          const updatedChats = [...prev, ...newChats];

          // Notify parent with updated chats
          if (onChatsLoaded) {
            onChatsLoaded(updatedChats);
          }

          return updatedChats;
        });
      }

      setCurrentPage(page);
      setTotalItems(response.totalItems);
      setHasMorePages(page < response.totalPages);

    } catch (error) {
      console.error('Failed to load chats:', error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
      isLoadingRef.current = false;
    }
  };

  // Infinite scroll handler
  const handleScroll = useCallback(() => {
    const container = scrollContainerRef.current;
    if (!container || loadingMore || !hasMorePages || isLoadingRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } = container;
    const threshold = 100; // Load more when 100px from bottom

    if (scrollTop + clientHeight >= scrollHeight - threshold) {
      loadChats(currentPage + 1, false);
    }
  }, [loadingMore, hasMorePages, currentPage, chats]);

  // Attach scroll listener
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  // Reset pagination when search term changes
  useEffect(() => {
    if (searchTerm) {
      // For search, we'll use client-side filtering for now
      // In production, you might want to implement server-side search with pagination
      return;
    } else {
      // Reset to first page when search is cleared
      loadChats(1, true);
    }
  }, [searchTerm]);

  const filteredChats = chats.filter(chat =>
    chat.character?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    chat.latestMessage?.content.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatMessagePreview = (content: string, maxLength: number = 50) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  const formatTime = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true, locale: id });
    } catch {
      return '';
    }
  };

  if (loading) {
    return (
      <div className="w-80 border-r bg-background">
        <div className="h-20 p-4 border-b flex items-center">
          <div className="h-10 bg-muted animate-pulse rounded" />
        </div>
        <div className="space-y-2 p-2">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="p-3 rounded-lg animate-pulse">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-muted rounded-full" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-muted rounded w-3/4" />
                  <div className="h-3 bg-muted rounded w-1/2" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      {/* Header - Fixed */}
      <div className="flex-shrink-0 h-20 p-4 border-b bg-background/50 flex flex-col justify-center">
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-lg font-semibold">Chats</h2>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-xs text-muted-foreground">
              {searchTerm ? filteredChats.length : `${chats.length}${totalItems > chats.length ? `/${totalItems}` : ''}`}
            </span>
          </div>
        </div>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search chats..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 rounded-xl border-2 focus:border-bestieku-primary transition-colors"
          />
        </div>
      </div>

      {/* Chat List - Scrollable */}
      <div
        ref={scrollContainerRef}
        className="flex-1 overflow-y-auto overflow-x-hidden"
      >
        <div className="h-full">
          {filteredChats.length === 0 ? (
            <div className="p-8 text-center text-muted-foreground">
              <MessageCircle className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p className="text-sm">
                {searchTerm ? 'No chats found' : 'No chats yet'}
              </p>
              <p className="text-xs mt-1">
                {searchTerm ? 'Try a different search term' : 'Start a conversation with a character'}
              </p>
            </div>
          ) : (
            <div className="space-y-1 p-2">
            {filteredChats.map((chat) => (
              <div
                key={chat.id}
                onClick={() => onChatSelect(chat)}
                className={`p-3 rounded-xl cursor-pointer transition-all duration-200 hover:bg-muted/50 hover:scale-[1.02] ${
                  selectedChatId === chat.id
                    ? 'bg-bestieku-primary/10 border-2 border-bestieku-primary/30 shadow-md'
                    : 'border-2 border-transparent hover:border-muted'
                }`}
              >
                <div className="flex items-center space-x-3">
                  {/* Character Avatar */}
                  <div className="relative">
                    <Avatar className="w-12 h-12 ring-2 ring-bestieku-primary/20">
                      <AvatarImage src={chat.character?.image} alt={chat.character?.name} />
                      <AvatarFallback className="bg-bestieku-primary/10 text-bestieku-primary font-semibold">
                        {chat.character?.name?.charAt(0)?.toUpperCase() || 'C'}
                      </AvatarFallback>
                    </Avatar>
                    {/* Online indicator */}
                    <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-background rounded-full"></div>
                  </div>

                  {/* Chat Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h3 className="font-medium text-sm truncate">
                        {chat.character?.name || 'Unknown Character'}
                      </h3>
                      {chat.latestMessage && (
                        <span className="text-xs text-muted-foreground">
                          {formatTime(chat.latestMessage.createdAt)}
                        </span>
                      )}
                    </div>
                    
                    {chat.latestMessage ? (
                      <p className="text-sm text-muted-foreground truncate">
                        {chat.latestMessage.role === 'user' ? 'You: ' : ''}
                        {formatMessagePreview(chat.latestMessage.content)}
                      </p>
                    ) : (
                      <p className="text-sm text-muted-foreground italic">
                        Belum ada pesan
                      </p>
                    )}

                    {/* Message Count Badge */}
                    {chat.messageCount > 0 && (
                      <div className="flex items-center justify-between mt-2">
                        <Badge variant="outline" className="text-xs">
                          {chat.messageCount} pesan
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}

            {/* Loading More Indicator */}
            {loadingMore && (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="w-5 h-5 animate-spin text-muted-foreground" />
                <span className="ml-2 text-sm text-muted-foreground">Memuat lebih banyak percakapan...</span>
              </div>
            )}

            {/* End of List Indicator */}
            {!loading && !loadingMore && !hasMorePages && chats.length > 0 && !searchTerm && (
              <div className="text-center py-4 text-xs text-muted-foreground">
                Kamu sudah sampai di akhir list percakapan.
              </div>
            )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
