"use client"

import { useState, useEffect } from "react"
import {
  BadgeCheck,
  Bell,
  ChevronsUpDown,
  CreditCard,
  LogOut,
  Palette,
  <PERSON>rkles,
  User,
  Co<PERSON>,
} from "lucide-react"

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"
import { Button } from "@/components/ui/button"
import { useAuth } from "@/contexts/auth-context"
import { AuthModal } from "@/components/auth/auth-modal"
import { ModeToggleSimple } from "@/components/mode-toggle"
import { useTheme } from "next-themes"
import { creditService } from "@/services/credit"
import { CreditBalance } from "@/types/credit"

export function NavUser() {
  const { isMobile } = useSidebar()
  const { user, isAuthenticated, logout } = useAuth()
  const { setTheme } = useTheme()
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false)
  const [creditBalance, setCreditBalance] = useState<CreditBalance | null>(null)

  // Load credit balance when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      loadCreditBalance()
    } else {
      setCreditBalance(null)
    }
  }, [isAuthenticated])

  const loadCreditBalance = async () => {
    try {
      const balance = await creditService.getCreditBalance()
      setCreditBalance(balance)
    } catch (error) {
      console.error('Failed to load credit balance:', error)
    }
  }

  if (!isAuthenticated) {
    return (
      <>
        <div className="space-y-3">
          {/* Theme toggle temporarily hidden */}
          {/* <div className="flex items-center justify-center">
            <ModeToggleSimple />
          </div> */}

          <Button
            onClick={() => setIsAuthModalOpen(true)}
            variant="outline"
            className="w-full h-12 border-2 border-bestieku-primary hover:bg-bestieku-primary/10 rounded-xl transition-all duration-300 hover:scale-[1.02]"
          >
            <User className="mr-2 h-5 w-5" />
            <span className="font-medium">Mulai Sekarang</span>
          </Button>
        </div>
        <AuthModal
          isOpen={isAuthModalOpen}
          onClose={() => setIsAuthModalOpen(false)}
        />
      </>
    )
  }

  return (
    <>
      <div className="space-y-3">
        {/* Theme toggle temporarily hidden */}
        {/* <div className="flex items-center justify-center">
          <ModeToggleSimple />
        </div> */}

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="w-full p-3 rounded-xl bg-gradient-to-r from-muted/50 to-muted/30 hover:from-bestieku-primary/10 hover:to-bestieku-primary-dark/10 border border-border/50 hover:border-bestieku-primary/30 transition-all duration-200 hover:scale-[1.02] group">
              <div className="flex items-center gap-3">
                <Avatar className="h-10 w-10 ring-2 ring-bestieku-primary/20 group-hover:ring-bestieku-primary/40 transition-all duration-200">
                  <AvatarImage src={user?.image} alt={user?.name} />
                  <AvatarFallback className="bg-gradient-bestieku font-semibold">
                    {user?.name?.charAt(0)?.toUpperCase() || 'U'}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 text-left min-w-0">
                  <div className="font-medium text-sm truncate">{user?.name}</div>
                  <div className="text-xs text-muted-foreground truncate">{user?.email}</div>
                </div>
                <ChevronsUpDown className="size-4 text-muted-foreground group-hover:text-bestieku-primary transition-colors" />
              </div>
            </button>
          </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-64 rounded-xl border-border/50 shadow-xl"
              side={isMobile ? "bottom" : "right"}
              align="end"
              sideOffset={8}
            >
              <DropdownMenuLabel className="p-4 font-normal">
                <div className="flex items-center gap-3">
                  <Avatar className="h-12 w-12 ring-2 ring-bestieku-primary/20">
                    <AvatarImage src={user?.image} alt={user?.name} />
                    <AvatarFallback className="bg-gradient-bestieku font-semibold">
                      {user?.name?.charAt(0)?.toUpperCase() || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <div className="font-semibold truncate">{user?.name}</div>
                    <div className="text-sm text-muted-foreground truncate">{user?.email}</div>
                    {creditBalance && (
                      <div className="flex items-center gap-1 mt-1">
                        <Coins className="w-3 h-3 text-bestieku-primary" />
                        <span className="text-xs font-medium text-bestieku-primary">
                          {creditBalance.balance.toLocaleString('id-ID')} B Coin
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                <DropdownMenuItem
                  className="hover:bg-bestieku-primary/10 hover:text-bestieku-primary focus:bg-bestieku-primary/10 focus:text-bestieku-primary cursor-pointer"
                  onClick={() => window.location.href = '/credits'}
                >
                  <Coins />
                  B Coin
                  {creditBalance && (
                    <span className="ml-auto text-xs bg-bestieku-primary/20 text-bestieku-primary px-2 py-0.5 rounded-full">
                      {creditBalance.balance.toLocaleString('id-ID')}
                    </span>
                  )}
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="hover:bg-bestieku-primary/10 hover:text-bestieku-primary focus:bg-bestieku-primary/10 focus:text-bestieku-primary cursor-pointer"
                  onClick={() => window.location.href = '/settings'}
                >
                  <BadgeCheck />
                  Akun
                </DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuSeparator />
              {/* Theme options temporarily hidden */}
              <DropdownMenuItem onClick={logout} className="hover:!bg-red-50 hover:!text-red-600 focus:!bg-red-50 focus:!text-red-600">
                <LogOut />
                Keluar
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
      </div>
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
      />
    </>
  )
}
