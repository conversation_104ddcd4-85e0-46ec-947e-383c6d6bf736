export interface Message {
  id: string;
  chatId?: string;
  role: 'user' | 'assistant';
  content: string;
  contentType: string;
  createdAt: string;
  updatedAt: string;
}

export interface Chat {
  id: string;
  userId: string;
  characterId: string;
  messageCount: number;
  latestMessage?: Message;
  platform: string;
  createdAt: string;
  updatedAt: string;
  // We'll populate this from character data
  character?: {
    id: string;
    name: string;
    image: string;
  };
}

export interface ChatsResponse {
  data: Chat[];
  totalItems: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
}

export interface MessagesResponse {
  data: Message[];
  totalItems: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
}

export interface SendMessageRequest {
  message: string;
  streaming?: boolean;
}

export interface SendMessageResponse {
  id: string;
  chatId: string;
  content: string;
  role: string;
  createdAt: string;
}

export interface GetChatsParams {
  page?: number;
  limit?: number;
  search?: string;
}

export interface GetMessagesParams {
  page?: number;
  limit?: number;
  search?: string;
}

// SSE Event types
export interface SSEEvent {
  event: 'start' | 'token' | 'metadata' | 'end';
  data: string | any;
}

export interface SSEMetadata {
  chatId: string;
  chatMessageId: string;
  question: string;
  sessionId: string;
  memoryType: string;
}
