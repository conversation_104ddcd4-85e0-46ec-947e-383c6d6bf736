"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/chat/chat-interface.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/chat-interface.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_chat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/chat */ \"(app-pages-browser)/./src/services/chat.ts\");\n/* harmony import */ var _services_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/character */ \"(app-pages-browser)/./src/services/character.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,RotateCcw,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/id.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst MessageContent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { content, role } = param;\n    if (role === 'user') {\n        // For user messages, just display as plain text with line breaks\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm whitespace-pre-wrap leading-relaxed\",\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 34,\n            columnNumber: 12\n        }, undefined);\n    }\n    // For AI messages, render as Markdown\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-sm leading-relaxed prose prose-sm max-w-none dark:prose-invert\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_8__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n            ],\n            components: {\n                // Customize paragraph styling\n                p: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-2 last:mb-0 leading-relaxed\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize emphasis (italic)\n                em: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        className: \"italic text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize strong (bold)\n                strong: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        className: \"font-semibold text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize code blocks\n                code: (param)=>{\n                    let { children, className } = param;\n                    const isInline = !className;\n                    if (isInline) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"bg-muted px-1.5 py-0.5 rounded text-xs font-mono\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 17\n                        }, void 0);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"block bg-muted p-3 rounded-lg text-xs font-mono overflow-x-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // Customize lists\n                ul: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                ol: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                li: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"text-sm\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize blockquotes\n                blockquote: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-muted pl-4 italic my-2\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 13\n                    }, void 0);\n                }\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n});\n_c = MessageContent;\nconst MessageItem = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s((param)=>{\n    let { message, characterImage, characterName } = param;\n    var _characterName_charAt;\n    _s();\n    const formatTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageItem.useCallback[formatTime]\": (dateString)=>{\n            try {\n                return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_10__.formatDistanceToNow)(new Date(dateString), {\n                    addSuffix: true,\n                    locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_11__.id\n                });\n            } catch (e) {\n                return '';\n            }\n        }\n    }[\"MessageItem.useCallback[formatTime]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n        children: [\n            message.role === 'assistant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                className: \"w-8 h-8 mb-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                        src: characterImage,\n                        alt: characterName\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                        className: \"text-xs bg-bestieku-primary/10 text-bestieku-primary\",\n                        children: (characterName === null || characterName === void 0 ? void 0 : (_characterName_charAt = characterName.charAt(0)) === null || _characterName_charAt === void 0 ? void 0 : _characterName_charAt.toUpperCase()) || 'C'\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl p-4 shadow-sm \".concat(message.role === 'user' ? 'bg-bestieku-primary text-white rounded-br-md' : 'bg-white dark:bg-muted border rounded-bl-md'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: message.content,\n                        role: message.role\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mt-2 \".concat(message.role === 'user' ? 'text-white/70' : 'text-muted-foreground'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: formatTime(message.createdAt)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, undefined),\n                            message.role === 'user' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, undefined);\n}, \"HvJHf3nLs/NeKKNMBW1x3UPtyJE=\"));\n_c1 = MessageItem;\nconst StreamingMessage = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { streamingMessage, characterImage, characterName } = param;\n    var _characterName_charAt;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 justify-start\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                className: \"w-8 h-8 mb-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                        src: characterImage,\n                        alt: characterName\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                        className: \"text-xs bg-bestieku-primary/10 text-bestieku-primary\",\n                        children: (characterName === null || characterName === void 0 ? void 0 : (_characterName_charAt = characterName.charAt(0)) === null || _characterName_charAt === void 0 ? void 0 : _characterName_charAt.toUpperCase()) || 'C'\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl rounded-bl-md p-4 bg-white dark:bg-muted border shadow-sm\",\n                children: [\n                    streamingMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: streamingMessage,\n                        role: \"assistant\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"Berpikir\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 ml-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-bestieku-primary rounded-full animate-bounce\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-bestieku-primary rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '0.1s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-bestieku-primary rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '0.2s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-2 text-muted-foreground\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 h-1 bg-bestieku-primary rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: \"Mengetik...\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 158,\n        columnNumber: 5\n    }, undefined);\n});\n_c2 = StreamingMessage;\nconst ChatInput = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s1((param)=>{\n    let { onSendMessage, disabled, characterName, isStreaming } = param;\n    _s1();\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Use ref to store current message to avoid stale closures\n    const messageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)('');\n    messageRef.current = newMessage;\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleSendMessage]\": ()=>{\n            const currentMessage = messageRef.current.trim();\n            if (!currentMessage || disabled) return;\n            onSendMessage(currentMessage);\n            setNewMessage('');\n            messageRef.current = '';\n        }\n    }[\"ChatInput.useCallback[handleSendMessage]\"], [\n        disabled,\n        onSendMessage\n    ]);\n    const handleKeyPress = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleKeyPress]\": (e)=>{\n            if (e.key === 'Enter' && !e.shiftKey) {\n                e.preventDefault();\n                handleSendMessage();\n            }\n        }\n    }[\"ChatInput.useCallback[handleKeyPress]\"], [\n        handleSendMessage\n    ]);\n    const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleInputChange]\": (e)=>{\n            const value = e.target.value;\n            setNewMessage(value);\n            messageRef.current = value;\n        }\n    }[\"ChatInput.useCallback[handleInputChange]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-shrink-0 border-t p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-end space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                ref: inputRef,\n                                value: newMessage,\n                                onChange: handleInputChange,\n                                onKeyPress: handleKeyPress,\n                                placeholder: \"Message \".concat(characterName || 'character', \"...\"),\n                                disabled: disabled,\n                                className: \"pr-12 py-3 rounded-2xl border-2 focus:border-bestieku-primary transition-colors\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                children: disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-4 h-4 border-2 border-bestieku-primary border-t-transparent rounded-full animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: handleSendMessage,\n                        disabled: !newMessage.trim() || disabled,\n                        className: \"bg-bestieku-primary hover:bg-bestieku-primary-dark rounded-2xl px-6 py-3 transition-all duration-200 hover:scale-105 disabled:hover:scale-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mt-2 text-xs text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Tekan Enter untuk mengirim, Shift+Enter untuk baris baru\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, undefined),\n                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-bestieku-primary animate-pulse\",\n                        children: \"AI sedang merespons...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 228,\n        columnNumber: 5\n    }, undefined);\n}, \"1o5B5G/jtGrG8z8Ow45dQHalZdE=\"));\n_c3 = ChatInput;\nconst ChatInterface = /*#__PURE__*/ _s2((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c4 = _s2((param)=>{\n    let { chat, onBack, onToggleProfile, onChatReset, selectedBackground } = param;\n    var _chat_character, _chat_character1, _chat_character2, _chat_character3, _chat_character4, _chat_character5, _chat_character_name_charAt, _chat_character_name, _chat_character6, _chat_character7;\n    _s2();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessage, setStreamingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [characterAssets, setCharacterAssets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [backgroundAssetUrl, setBackgroundAssetUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [character, setCharacter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamConnectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamingTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Delete chat messages function\n    const handleDeleteMessages = async ()=>{\n        if (!chat.id) return;\n        try {\n            setIsDeleting(true);\n            await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.deleteChatMessages(chat.id);\n            // Clear messages locally\n            setMessages([]);\n            // Show success toast\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Chat berhasil direset!\", {\n                description: \"Semua pesan telah dihapus. Anda bisa memulai percakapan baru.\",\n                duration: 4000\n            });\n            // Call parent callback if provided\n            if (onChatReset) {\n                onChatReset();\n            }\n        } catch (error) {\n            console.error('Failed to delete messages:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Gagal mereset chat\", {\n                description: \"Terjadi kesalahan saat menghapus pesan. Silakan coba lagi.\",\n                duration: 4000\n            });\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    // Stable references for character data to prevent unnecessary re-renders\n    const characterImageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)((_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image);\n    const characterNameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)((_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name);\n    // Load character data and assets\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const loadCharacterData = {\n                \"ChatInterface.useEffect.loadCharacterData\": async ()=>{\n                    try {\n                        // Load character details\n                        const characterData = await _services_character__WEBPACK_IMPORTED_MODULE_3__.characterService.getCharacterById(chat.characterId);\n                        setCharacter(characterData);\n                        // Load character assets\n                        const assets = await _services_character__WEBPACK_IMPORTED_MODULE_3__.characterService.getCharacterAssets(chat.characterId);\n                        setCharacterAssets(assets);\n                    } catch (error) {\n                        console.error('Failed to load character data:', error);\n                    }\n                }\n            }[\"ChatInterface.useEffect.loadCharacterData\"];\n            loadCharacterData();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        chat.characterId\n    ]);\n    // Update background URL when selectedBackground changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (selectedBackground && characterAssets.length > 0) {\n                const backgroundAsset = characterAssets.find({\n                    \"ChatInterface.useEffect.backgroundAsset\": (asset)=>asset.id === selectedBackground && asset.purpose === 'background'\n                }[\"ChatInterface.useEffect.backgroundAsset\"]);\n                setBackgroundAssetUrl((backgroundAsset === null || backgroundAsset === void 0 ? void 0 : backgroundAsset.url) || null);\n            } else {\n                setBackgroundAssetUrl(null);\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        selectedBackground,\n        characterAssets\n    ]);\n    // Detect mobile screen size\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkMobile = {\n                \"ChatInterface.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"ChatInterface.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"ChatInterface.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    // Update refs when chat changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            var _chat_character, _chat_character1;\n            characterImageRef.current = (_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image;\n            characterNameRef.current = (_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        (_chat_character2 = chat.character) === null || _chat_character2 === void 0 ? void 0 : _chat_character2.image,\n        (_chat_character3 = chat.character) === null || _chat_character3 === void 0 ? void 0 : _chat_character3.name\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            loadMessages();\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    // Cleanup stream connection on unmount\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    // Cleanup streaming timeout\n                    if (streamingTimeoutRef.current) {\n                        clearTimeout(streamingTimeoutRef.current);\n                    }\n                    // Cleanup stream timeout\n                    if (streamTimeoutRef.current) {\n                        clearTimeout(streamTimeoutRef.current);\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        chat.id\n    ]);\n    // Stable scroll function that doesn't cause re-renders\n    const scrollToBottomRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        \"ChatInterface.useRef[scrollToBottomRef]\": ()=>{}\n    }[\"ChatInterface.useRef[scrollToBottomRef]\"]);\n    scrollToBottomRef.current = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    // Only scroll when messages count changes, not on every render\n    const messagesCountRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(messages.length);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > messagesCountRef.current) {\n                messagesCountRef.current = messages.length;\n                const timeoutId = setTimeout({\n                    \"ChatInterface.useEffect.timeoutId\": ()=>{\n                        var _scrollToBottomRef_current;\n                        (_scrollToBottomRef_current = scrollToBottomRef.current) === null || _scrollToBottomRef_current === void 0 ? void 0 : _scrollToBottomRef_current.call(scrollToBottomRef);\n                    }\n                }[\"ChatInterface.useEffect.timeoutId\"], 100);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"ChatInterface.useEffect\"];\n            }\n            messagesCountRef.current = messages.length;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    // Separate effect for streaming - only scroll when streaming starts or ends\n    const isStreamingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(isStreaming);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (isStreaming && !isStreamingRef.current) {\n                // Streaming just started\n                const timeoutId = setTimeout({\n                    \"ChatInterface.useEffect.timeoutId\": ()=>{\n                        var _scrollToBottomRef_current;\n                        (_scrollToBottomRef_current = scrollToBottomRef.current) === null || _scrollToBottomRef_current === void 0 ? void 0 : _scrollToBottomRef_current.call(scrollToBottomRef);\n                    }\n                }[\"ChatInterface.useEffect.timeoutId\"], 200);\n                isStreamingRef.current = isStreaming;\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"ChatInterface.useEffect\"];\n            }\n            isStreamingRef.current = isStreaming;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        isStreaming\n    ]);\n    const loadMessages = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.getChatMessages(chat.id, {\n                limit: 50\n            });\n            setMessages(response.data.reverse()); // Reverse to show oldest first\n        } catch (error) {\n            console.error('Failed to load messages:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInterface.useCallback[handleSendMessage]\": async (messageText)=>{\n            if (!messageText.trim() || sending) return;\n            const tempId = \"temp-\".concat(Date.now());\n            setSending(true);\n            try {\n                // Add user message to UI immediately\n                const userMessage = {\n                    id: tempId,\n                    role: 'user',\n                    content: messageText,\n                    contentType: 'text',\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                };\n                setMessages({\n                    \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>[\n                            ...prev,\n                            userMessage\n                        ]\n                }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                console.log('Sending message to chat:', chat.id, {\n                    message: messageText,\n                    streaming: true\n                });\n                // Send message with streaming enabled\n                const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.sendMessage(chat.id, {\n                    message: messageText,\n                    streaming: true\n                });\n                console.log('Message sent successfully:', response);\n                // Update the temporary message with real ID if available\n                if (response.id) {\n                    setMessages({\n                        \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.map({\n                                \"ChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id === tempId ? {\n                                        ...msg,\n                                        id: response.id\n                                    } : msg\n                            }[\"ChatInterface.useCallback[handleSendMessage]\"])\n                    }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                }\n                // Start streaming response\n                await startStreaming();\n            } catch (error) {\n                console.error('Failed to send message:', error);\n                // Remove the temporary user message on error\n                setMessages({\n                    \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.filter({\n                            \"ChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id !== tempId\n                        }[\"ChatInterface.useCallback[handleSendMessage]\"])\n                }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                alert('Failed to send message. Please try again.');\n            } finally{\n                setSending(false);\n            }\n        }\n    }[\"ChatInterface.useCallback[handleSendMessage]\"], [\n        sending,\n        chat.id\n    ]);\n    const startStreaming = async ()=>{\n        console.log('Starting stream for chat:', chat.id);\n        setIsStreaming(true);\n        setStreamingMessage('');\n        // Close existing connection and clear timeouts\n        if (streamConnectionRef.current) {\n            streamConnectionRef.current.close();\n        }\n        if (streamingTimeoutRef.current) {\n            clearTimeout(streamingTimeoutRef.current);\n        }\n        if (streamTimeoutRef.current) {\n            clearTimeout(streamTimeoutRef.current);\n        }\n        let currentStreamingMessage = '';\n        // More aggressive throttling for streaming message updates\n        let lastUpdateTime = 0;\n        const updateStreamingMessage = (message)=>{\n            const now = Date.now();\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n            }\n            // Immediate update if it's been more than 100ms since last update\n            if (now - lastUpdateTime > 100) {\n                setStreamingMessage(message);\n                lastUpdateTime = now;\n            } else {\n                // Otherwise, throttle the update\n                streamingTimeoutRef.current = setTimeout(()=>{\n                    setStreamingMessage(message);\n                    lastUpdateTime = Date.now();\n                }, 100);\n            }\n        };\n        const onMessage = (data)=>{\n            console.log('Received SSE event:', data);\n            switch(data.event){\n                case 'start':\n                    console.log('Stream started');\n                    currentStreamingMessage = '';\n                    setStreamingMessage('');\n                    break;\n                case 'token':\n                    currentStreamingMessage += data.data;\n                    updateStreamingMessage(currentStreamingMessage);\n                    break;\n                case 'metadata':\n                    console.log('Stream metadata:', data.data);\n                    break;\n                case 'end':\n                    var _data_data;\n                    console.log('Stream ended, final message:', currentStreamingMessage);\n                    // Clear any pending streaming updates\n                    if (streamingTimeoutRef.current) {\n                        clearTimeout(streamingTimeoutRef.current);\n                    }\n                    // Clear stream timeout to prevent false timeout errors\n                    if (streamTimeoutRef.current) {\n                        clearTimeout(streamTimeoutRef.current);\n                    }\n                    // Finalize the streaming message\n                    const finalMessage = {\n                        id: ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.chatMessageId) || \"msg-\".concat(Date.now()),\n                        role: 'assistant',\n                        content: currentStreamingMessage,\n                        contentType: 'text',\n                        createdAt: new Date().toISOString(),\n                        updatedAt: new Date().toISOString()\n                    };\n                    setMessages((prev)=>[\n                            ...prev,\n                            finalMessage\n                        ]);\n                    setStreamingMessage('');\n                    setIsStreaming(false);\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    break;\n                default:\n                    console.log('Unknown SSE event:', data.event);\n            }\n        };\n        const onError = async (error)=>{\n            var _error_message;\n            console.error('Stream Error:', error);\n            // Clear timeouts first to prevent further errors\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n            }\n            if (streamTimeoutRef.current) {\n                clearTimeout(streamTimeoutRef.current);\n            }\n            setIsStreaming(false);\n            setStreamingMessage('');\n            if (streamConnectionRef.current) {\n                streamConnectionRef.current.close();\n            }\n            // Only reload messages if it's not a timeout error and streaming was actually active\n            if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('timeout')) && isStreaming) {\n                console.log('Attempting to reload messages as fallback...');\n                try {\n                    await loadMessages();\n                } catch (reloadError) {\n                    console.error('Failed to reload messages:', reloadError);\n                // Don't show alert for timeout errors to avoid disrupting user\n                }\n            }\n        };\n        try {\n            // Create new stream connection\n            const connection = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.createStreamConnection(chat.id, onMessage, onError);\n            streamConnectionRef.current = connection;\n            // Set timeout for streaming (30 seconds) - but store reference to clear it\n            streamTimeoutRef.current = setTimeout(()=>{\n                // Only trigger timeout if streaming is still active and connection exists\n                if (isStreaming && streamConnectionRef.current) {\n                    console.log('Stream timeout, falling back to message reload');\n                    onError(new Error('Stream timeout'));\n                }\n            }, 30000);\n        } catch (error) {\n            console.error('Failed to create stream connection:', error);\n            setIsStreaming(false);\n            alert('Failed to establish connection for AI response. Please try again.');\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 h-20 border-b p-4 animate-pulse flex items-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-muted rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 622,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded w-32\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-muted rounded w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 623,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 621,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 620,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-4 space-y-4 overflow-y-auto\",\n                    children: Array.from({\n                        length: 3\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-muted rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 633,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, i, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 631,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 629,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 619,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 h-20 border-b p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 flex items-center justify-between w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            isMobile && onBack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: onBack,\n                                className: \"hover:bg-muted/50 md:hidden\",\n                                \"aria-label\": \"Back to chat list\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 658,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 651,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 \".concat(isMobile && onToggleProfile ? 'cursor-pointer hover:bg-muted/50 rounded-lg p-2 -m-2 transition-colors' : ''),\n                                onClick: isMobile && onToggleProfile ? onToggleProfile : undefined,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                                                className: \"w-12 h-12 ring-2 ring-bestieku-primary/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                                                        src: (_chat_character4 = chat.character) === null || _chat_character4 === void 0 ? void 0 : _chat_character4.image,\n                                                        alt: (_chat_character5 = chat.character) === null || _chat_character5 === void 0 ? void 0 : _chat_character5.name\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 670,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                                                        className: \"bg-bestieku-primary/10 text-bestieku-primary font-semibold\",\n                                                        children: ((_chat_character6 = chat.character) === null || _chat_character6 === void 0 ? void 0 : (_chat_character_name = _chat_character6.name) === null || _chat_character_name === void 0 ? void 0 : (_chat_character_name_charAt = _chat_character_name.charAt(0)) === null || _chat_character_name_charAt === void 0 ? void 0 : _chat_character_name_charAt.toUpperCase()) || 'C'\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 671,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-background rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                lineNumber: 676,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 668,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"font-semibold text-lg\",\n                                                children: ((_chat_character7 = chat.character) === null || _chat_character7 === void 0 ? void 0 : _chat_character7.name) || 'Unknown Character'\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                lineNumber: 679,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            chat.messageCount,\n                                                            \" pesan\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 681,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-500\",\n                                                        children: \"Online\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 683,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                lineNumber: 680,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 678,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 648,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            isMobile && onToggleProfile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: onToggleProfile,\n                                className: \"hover:bg-muted/50 md:hidden\",\n                                \"aria-label\": \"View character profile\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 699,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 692,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: handleDeleteMessages,\n                                disabled: isDeleting || messages.length === 0,\n                                className: \"text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_RotateCcw_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 711,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    isDeleting ? 'Mereset...' : 'Reset'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 704,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 689,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 647,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto overflow-x-hidden p-4 space-y-4 relative\",\n                style: {\n                    background: backgroundAssetUrl ? \"linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.8)), url(\".concat(backgroundAssetUrl, \")\") : 'linear-gradient(to bottom, hsl(var(--background)), hsl(var(--muted))/0.2)',\n                    backgroundSize: backgroundAssetUrl ? 'cover' : 'auto',\n                    backgroundPosition: backgroundAssetUrl ? 'center' : 'auto',\n                    backgroundRepeat: backgroundAssetUrl ? 'no-repeat' : 'auto'\n                },\n                children: [\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageItem, {\n                            message: message,\n                            characterImage: characterImageRef.current,\n                            characterName: characterNameRef.current\n                        }, message.id, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 730,\n                            columnNumber: 11\n                        }, undefined)),\n                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamingMessage, {\n                        streamingMessage: streamingMessage,\n                        characterImage: characterImageRef.current,\n                        characterName: characterNameRef.current\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 740,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 747,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 718,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatInput, {\n                onSendMessage: handleSendMessage,\n                disabled: sending || isStreaming,\n                characterName: characterNameRef.current,\n                isStreaming: isStreaming\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 751,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 645,\n        columnNumber: 5\n    }, undefined);\n}, \"wFJ1SEqwOF5CTh2UO5d9ZjLHLFo=\")), \"wFJ1SEqwOF5CTh2UO5d9ZjLHLFo=\");\n_c5 = ChatInterface;\nChatInterface.displayName = 'ChatInterface';\n\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"MessageContent\");\n$RefreshReg$(_c1, \"MessageItem\");\n$RefreshReg$(_c2, \"StreamingMessage\");\n$RefreshReg$(_c3, \"ChatInput\");\n$RefreshReg$(_c4, \"ChatInterface$memo\");\n$RefreshReg$(_c5, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2NoYXQvY2hhdC1pbnRlcmZhY2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRThFO0FBR2hDO0FBQ1U7QUFDUjtBQUNGO0FBQytCO0FBQ2I7QUFDakI7QUFDVjtBQUNNO0FBQ1I7QUFDSjtBQWUvQixNQUFNc0IsK0JBQWlCakIsMkNBQUlBLENBQUM7UUFBQyxFQUFFa0IsT0FBTyxFQUFFQyxJQUFJLEVBQXVCO0lBQ2pFLElBQUlBLFNBQVMsUUFBUTtRQUNuQixpRUFBaUU7UUFDakUscUJBQU8sOERBQUNDO1lBQUVDLFdBQVU7c0JBQStDSDs7Ozs7O0lBQ3JFO0lBRUEsc0NBQXNDO0lBQ3RDLHFCQUNFLDhEQUFDSTtRQUFJRCxXQUFVO2tCQUNiLDRFQUFDUCxvREFBYUE7WUFDWlMsZUFBZTtnQkFBQ1Isa0RBQVNBO2FBQUM7WUFDMUJTLFlBQVk7Z0JBQ1YsOEJBQThCO2dCQUM5QkosR0FBRzt3QkFBQyxFQUFFSyxRQUFRLEVBQUU7eUNBQ2QsOERBQUNMO3dCQUFFQyxXQUFVO2tDQUFrQ0k7Ozs7Ozs7Z0JBRWpELDhCQUE4QjtnQkFDOUJDLElBQUk7d0JBQUMsRUFBRUQsUUFBUSxFQUFFO3lDQUNmLDhEQUFDQzt3QkFBR0wsV0FBVTtrQ0FBdUJJOzs7Ozs7O2dCQUV2QywwQkFBMEI7Z0JBQzFCRSxRQUFRO3dCQUFDLEVBQUVGLFFBQVEsRUFBRTt5Q0FDbkIsOERBQUNFO3dCQUFPTixXQUFVO2tDQUE4Qkk7Ozs7Ozs7Z0JBRWxELHdCQUF3QjtnQkFDeEJHLE1BQU07d0JBQUMsRUFBRUgsUUFBUSxFQUFFSixTQUFTLEVBQUU7b0JBQzVCLE1BQU1RLFdBQVcsQ0FBQ1I7b0JBQ2xCLElBQUlRLFVBQVU7d0JBQ1oscUJBQ0UsOERBQUNEOzRCQUFLUCxXQUFVO3NDQUNiSTs7Ozs7O29CQUdQO29CQUNBLHFCQUNFLDhEQUFDRzt3QkFBS1AsV0FBVTtrQ0FDYkk7Ozs7OztnQkFHUDtnQkFDQSxrQkFBa0I7Z0JBQ2xCSyxJQUFJO3dCQUFDLEVBQUVMLFFBQVEsRUFBRTt5Q0FDZiw4REFBQ0s7d0JBQUdULFdBQVU7a0NBQXdDSTs7Ozs7OztnQkFFeERNLElBQUk7d0JBQUMsRUFBRU4sUUFBUSxFQUFFO3lDQUNmLDhEQUFDTTt3QkFBR1YsV0FBVTtrQ0FBMkNJOzs7Ozs7O2dCQUUzRE8sSUFBSTt3QkFBQyxFQUFFUCxRQUFRLEVBQUU7eUNBQ2YsOERBQUNPO3dCQUFHWCxXQUFVO2tDQUFXSTs7Ozs7OztnQkFFM0Isd0JBQXdCO2dCQUN4QlEsWUFBWTt3QkFBQyxFQUFFUixRQUFRLEVBQUU7eUNBQ3ZCLDhEQUFDUTt3QkFBV1osV0FBVTtrQ0FDbkJJOzs7Ozs7O1lBR1A7c0JBRUNQOzs7Ozs7Ozs7OztBQUlUO0tBOURNRDtBQXNFTixNQUFNaUIsNEJBQWNsQywyQ0FBSUEsSUFBQztRQUFDLEVBQUVtQyxPQUFPLEVBQUVDLGNBQWMsRUFBRUMsYUFBYSxFQUFvQjtRQWtCekVBOztJQWpCWCxNQUFNQyxhQUFhdkMsa0RBQVdBOytDQUFDLENBQUN3QztZQUM5QixJQUFJO2dCQUNGLE9BQU8zQix5R0FBbUJBLENBQUMsSUFBSTRCLEtBQUtELGFBQWE7b0JBQUVFLFdBQVc7b0JBQU1DLFFBQVE3QixnREFBRUE7Z0JBQUM7WUFDakYsRUFBRSxVQUFNO2dCQUNOLE9BQU87WUFDVDtRQUNGOzhDQUFHLEVBQUU7SUFFTCxxQkFDRSw4REFBQ1M7UUFDQ0QsV0FBVyx3QkFBa0YsT0FBMURjLFFBQVFoQixJQUFJLEtBQUssU0FBUyxnQkFBZ0I7O1lBRzVFZ0IsUUFBUWhCLElBQUksS0FBSyw2QkFDaEIsOERBQUNkLHlEQUFNQTtnQkFBQ2dCLFdBQVU7O2tDQUNoQiw4REFBQ2QsOERBQVdBO3dCQUFDb0MsS0FBS1A7d0JBQWdCUSxLQUFLUDs7Ozs7O2tDQUN2Qyw4REFBQy9CLGlFQUFjQTt3QkFBQ2UsV0FBVTtrQ0FDdkJnQixDQUFBQSwwQkFBQUEscUNBQUFBLHdCQUFBQSxjQUFlUSxNQUFNLENBQUMsZ0JBQXRCUiw0Q0FBQUEsc0JBQTBCUyxXQUFXLE9BQU07Ozs7Ozs7Ozs7OzswQkFLbEQsOERBQUN4QjtnQkFDQ0QsV0FBVyx5Q0FJVixPQUhDYyxRQUFRaEIsSUFBSSxLQUFLLFNBQ2IsaURBQ0E7O2tDQUdOLDhEQUFDRjt3QkFBZUMsU0FBU2lCLFFBQVFqQixPQUFPO3dCQUFFQyxNQUFNZ0IsUUFBUWhCLElBQUk7Ozs7OztrQ0FDNUQsOERBQUNHO3dCQUFJRCxXQUFXLDBDQUVmLE9BRENjLFFBQVFoQixJQUFJLEtBQUssU0FBUyxrQkFBa0I7OzBDQUU1Qyw4REFBQ0M7Z0NBQUVDLFdBQVU7MENBQ1ZpQixXQUFXSCxRQUFRWSxTQUFTOzs7Ozs7NEJBRTlCWixRQUFRaEIsSUFBSSxLQUFLLHdCQUNoQiw4REFBQ0c7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDQzt3Q0FBSUQsV0FBVTs7Ozs7O2tEQUNmLDhEQUFDQzt3Q0FBSUQsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTzdCO01BL0NNYTtBQXVETixNQUFNYyxpQ0FBbUJoRCwyQ0FBSUEsQ0FBQztRQUFDLEVBQUVpRCxnQkFBZ0IsRUFBRWIsY0FBYyxFQUFFQyxhQUFhLEVBQXlCO1FBTTlGQTtJQUxULHFCQUNFLDhEQUFDZjtRQUFJRCxXQUFVOzswQkFDYiw4REFBQ2hCLHlEQUFNQTtnQkFBQ2dCLFdBQVU7O2tDQUNoQiw4REFBQ2QsOERBQVdBO3dCQUFDb0MsS0FBS1A7d0JBQWdCUSxLQUFLUDs7Ozs7O2tDQUN2Qyw4REFBQy9CLGlFQUFjQTt3QkFBQ2UsV0FBVTtrQ0FDdkJnQixDQUFBQSwwQkFBQUEscUNBQUFBLHdCQUFBQSxjQUFlUSxNQUFNLENBQUMsZ0JBQXRCUiw0Q0FBQUEsc0JBQTBCUyxXQUFXLE9BQU07Ozs7Ozs7Ozs7OzswQkFJaEQsOERBQUN4QjtnQkFBSUQsV0FBVTs7b0JBQ1o0QixpQ0FDQyw4REFBQ2hDO3dCQUFlQyxTQUFTK0I7d0JBQWtCOUIsTUFBSzs7Ozs7a0RBRWhELDhEQUFDRzt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUM2QjtnQ0FBSzdCLFdBQVU7MENBQWdDOzs7Ozs7MENBQ2hELDhEQUFDQztnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNDO3dDQUFJRCxXQUFVOzs7Ozs7a0RBQ2YsOERBQUNDO3dDQUFJRCxXQUFVO3dDQUEwRDhCLE9BQU87NENBQUVDLGdCQUFnQjt3Q0FBTzs7Ozs7O2tEQUN6Ryw4REFBQzlCO3dDQUFJRCxXQUFVO3dDQUEwRDhCLE9BQU87NENBQUVDLGdCQUFnQjt3Q0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUsvRyw4REFBQzlCO3dCQUFJRCxXQUFVO2tDQUNiLDRFQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNDO29DQUFJRCxXQUFVOzs7Ozs7OENBQ2YsOERBQUM2QjtvQ0FBSzdCLFdBQVU7OENBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXRDO01BakNNMkI7QUEwQ04sTUFBTUssMEJBQVlyRCwyQ0FBSUEsS0FBQztRQUFDLEVBQUVzRCxhQUFhLEVBQUVDLFFBQVEsRUFBRWxCLGFBQWEsRUFBRW1CLFdBQVcsRUFBa0I7O0lBQzdGLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHOUQsK0NBQVFBLENBQUM7SUFDN0MsTUFBTStELFdBQVc3RCw2Q0FBTUEsQ0FBbUI7SUFFMUMsMkRBQTJEO0lBQzNELE1BQU04RCxhQUFhOUQsNkNBQU1BLENBQUM7SUFDMUI4RCxXQUFXQyxPQUFPLEdBQUdKO0lBRXJCLE1BQU1LLG9CQUFvQi9ELGtEQUFXQTtvREFBQztZQUNwQyxNQUFNZ0UsaUJBQWlCSCxXQUFXQyxPQUFPLENBQUNHLElBQUk7WUFDOUMsSUFBSSxDQUFDRCxrQkFBa0JSLFVBQVU7WUFDakNELGNBQWNTO1lBQ2RMLGNBQWM7WUFDZEUsV0FBV0MsT0FBTyxHQUFHO1FBQ3ZCO21EQUFHO1FBQUNOO1FBQVVEO0tBQWM7SUFFNUIsTUFBTVcsaUJBQWlCbEUsa0RBQVdBO2lEQUFDLENBQUNtRTtZQUNsQyxJQUFJQSxFQUFFQyxHQUFHLEtBQUssV0FBVyxDQUFDRCxFQUFFRSxRQUFRLEVBQUU7Z0JBQ3BDRixFQUFFRyxjQUFjO2dCQUNoQlA7WUFDRjtRQUNGO2dEQUFHO1FBQUNBO0tBQWtCO0lBRXRCLE1BQU1RLG9CQUFvQnZFLGtEQUFXQTtvREFBQyxDQUFDbUU7WUFDckMsTUFBTUssUUFBUUwsRUFBRU0sTUFBTSxDQUFDRCxLQUFLO1lBQzVCYixjQUFjYTtZQUNkWCxXQUFXQyxPQUFPLEdBQUdVO1FBQ3ZCO21EQUFHLEVBQUU7SUFFTCxxQkFDRSw4REFBQ2pEO1FBQUlELFdBQVU7OzBCQUNiLDhEQUFDQztnQkFBSUQsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ2pCLHVEQUFLQTtnQ0FDSnFFLEtBQUtkO2dDQUNMWSxPQUFPZDtnQ0FDUGlCLFVBQVVKO2dDQUNWSyxZQUFZVjtnQ0FDWlcsYUFBYSxXQUF3QyxPQUE3QnZDLGlCQUFpQixhQUFZO2dDQUNyRGtCLFVBQVVBO2dDQUNWbEMsV0FBVTs7Ozs7OzBDQUdaLDhEQUFDQztnQ0FBSUQsV0FBVTswQ0FDWmtDLDBCQUNDLDhEQUFDakM7b0NBQUlELFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUtyQiw4REFBQ2xCLHlEQUFNQTt3QkFDTDBFLFNBQVNmO3dCQUNUUCxVQUFVLENBQUNFLFdBQVdPLElBQUksTUFBTVQ7d0JBQ2hDbEMsV0FBVTtrQ0FFViw0RUFBQ2IsMEdBQUlBOzRCQUFDYSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzswQkFLcEIsOERBQUNDO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQzZCO2tDQUFLOzs7Ozs7b0JBQ0xNLDZCQUNDLDhEQUFDTjt3QkFBSzdCLFdBQVU7a0NBQXNDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLaEU7TUFwRU1nQztBQXNFTixNQUFNeUIsOEJBQWdCOUUsSUFBQUEsMkNBQUlBLFdBQUM7UUFBQyxFQUFFK0UsSUFBSSxFQUFFQyxNQUFNLEVBQUVDLGVBQWUsRUFBRUMsV0FBVyxFQUFFQyxrQkFBa0IsRUFBc0I7UUFpRC9FSixpQkFDREEsa0JBZ0Q1QkEsa0JBQXVCQSxrQkFnVEtBLGtCQUE0QkEsa0JBRTNDQSw2QkFBQUEsc0JBQUFBLGtCQU9rQ0E7O0lBMVpuRCxNQUFNLENBQUNLLFVBQVVDLFlBQVksR0FBR3pGLCtDQUFRQSxDQUFZLEVBQUU7SUFDdEQsTUFBTSxDQUFDMEYsU0FBU0MsV0FBVyxHQUFHM0YsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDNEYsU0FBU0MsV0FBVyxHQUFHN0YsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDcUQsa0JBQWtCeUMsb0JBQW9CLEdBQUc5RiwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUM0RCxhQUFhbUMsZUFBZSxHQUFHL0YsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDZ0csVUFBVUMsWUFBWSxHQUFHakcsK0NBQVFBLENBQUM7SUFDekMsTUFBTSxDQUFDa0csaUJBQWlCQyxtQkFBbUIsR0FBR25HLCtDQUFRQSxDQUFtQixFQUFFO0lBQzNFLE1BQU0sQ0FBQ29HLG9CQUFvQkMsc0JBQXNCLEdBQUdyRywrQ0FBUUEsQ0FBZ0I7SUFDNUUsTUFBTSxDQUFDc0csWUFBWUMsY0FBYyxHQUFHdkcsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDd0csV0FBV0MsYUFBYSxHQUFHekcsK0NBQVFBLENBQU07SUFDaEQsTUFBTTBHLGlCQUFpQnhHLDZDQUFNQSxDQUFpQjtJQUM5QyxNQUFNeUcsc0JBQXNCekcsNkNBQU1BLENBQStCO0lBQ2pFLE1BQU0wRyxzQkFBc0IxRyw2Q0FBTUEsQ0FBd0I7SUFDMUQsTUFBTTJHLG1CQUFtQjNHLDZDQUFNQSxDQUF3QjtJQUV2RCxnQ0FBZ0M7SUFDaEMsTUFBTTRHLHVCQUF1QjtRQUMzQixJQUFJLENBQUMzQixLQUFLbEUsRUFBRSxFQUFFO1FBRWQsSUFBSTtZQUNGc0YsY0FBYztZQUNkLE1BQU1sRyx1REFBV0EsQ0FBQzBHLGtCQUFrQixDQUFDNUIsS0FBS2xFLEVBQUU7WUFFNUMseUJBQXlCO1lBQ3pCd0UsWUFBWSxFQUFFO1lBRWQscUJBQXFCO1lBQ3JCckUseUNBQUtBLENBQUM0RixPQUFPLENBQUMsMEJBQTBCO2dCQUN0Q0MsYUFBYTtnQkFDYkMsVUFBVTtZQUNaO1lBRUEsbUNBQW1DO1lBQ25DLElBQUk1QixhQUFhO2dCQUNmQTtZQUNGO1FBQ0YsRUFBRSxPQUFPNkIsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsOEJBQThCQTtZQUM1Qy9GLHlDQUFLQSxDQUFDK0YsS0FBSyxDQUFDLHNCQUFzQjtnQkFDaENGLGFBQWE7Z0JBQ2JDLFVBQVU7WUFDWjtRQUNGLFNBQVU7WUFDUlgsY0FBYztRQUNoQjtJQUNGO0lBRUEseUVBQXlFO0lBQ3pFLE1BQU1jLG9CQUFvQm5ILDZDQUFNQSxFQUFDaUYsa0JBQUFBLEtBQUtxQixTQUFTLGNBQWRyQixzQ0FBQUEsZ0JBQWdCbUMsS0FBSztJQUN0RCxNQUFNQyxtQkFBbUJySCw2Q0FBTUEsRUFBQ2lGLG1CQUFBQSxLQUFLcUIsU0FBUyxjQUFkckIsdUNBQUFBLGlCQUFnQnFDLElBQUk7SUFFcEQsaUNBQWlDO0lBQ2pDdkgsZ0RBQVNBO21DQUFDO1lBQ1IsTUFBTXdIOzZEQUFvQjtvQkFDeEIsSUFBSTt3QkFDRix5QkFBeUI7d0JBQ3pCLE1BQU1DLGdCQUFnQixNQUFNcEgsaUVBQWdCQSxDQUFDcUgsZ0JBQWdCLENBQUN4QyxLQUFLeUMsV0FBVzt3QkFDOUVuQixhQUFhaUI7d0JBRWIsd0JBQXdCO3dCQUN4QixNQUFNRyxTQUFTLE1BQU12SCxpRUFBZ0JBLENBQUN3SCxrQkFBa0IsQ0FBQzNDLEtBQUt5QyxXQUFXO3dCQUN6RXpCLG1CQUFtQjBCO29CQUNyQixFQUFFLE9BQU9WLE9BQU87d0JBQ2RDLFFBQVFELEtBQUssQ0FBQyxrQ0FBa0NBO29CQUNsRDtnQkFDRjs7WUFFQU07UUFDRjtrQ0FBRztRQUFDdEMsS0FBS3lDLFdBQVc7S0FBQztJQUVyQix3REFBd0Q7SUFDeEQzSCxnREFBU0E7bUNBQUM7WUFDUixJQUFJc0Ysc0JBQXNCVyxnQkFBZ0I2QixNQUFNLEdBQUcsR0FBRztnQkFDcEQsTUFBTUMsa0JBQWtCOUIsZ0JBQWdCK0IsSUFBSTsrREFDMUNDLENBQUFBLFFBQVNBLE1BQU1qSCxFQUFFLEtBQUtzRSxzQkFBc0IyQyxNQUFNQyxPQUFPLEtBQUs7O2dCQUVoRTlCLHNCQUFzQjJCLENBQUFBLDRCQUFBQSxzQ0FBQUEsZ0JBQWlCSSxHQUFHLEtBQUk7WUFDaEQsT0FBTztnQkFDTC9CLHNCQUFzQjtZQUN4QjtRQUNGO2tDQUFHO1FBQUNkO1FBQW9CVztLQUFnQjtJQUV4Qyw0QkFBNEI7SUFDNUJqRyxnREFBU0E7bUNBQUM7WUFDUixNQUFNb0k7dURBQWM7b0JBQ2xCcEMsWUFBWXFDLE9BQU9DLFVBQVUsR0FBRztnQkFDbEM7O1lBRUFGO1lBQ0FDLE9BQU9FLGdCQUFnQixDQUFDLFVBQVVIO1lBQ2xDOzJDQUFPLElBQU1DLE9BQU9HLG1CQUFtQixDQUFDLFVBQVVKOztRQUNwRDtrQ0FBRyxFQUFFO0lBRUwsZ0NBQWdDO0lBQ2hDcEksZ0RBQVNBO21DQUFDO2dCQUNvQmtGLGlCQUNEQTtZQUQzQmtDLGtCQUFrQnBELE9BQU8sSUFBR2tCLGtCQUFBQSxLQUFLcUIsU0FBUyxjQUFkckIsc0NBQUFBLGdCQUFnQm1DLEtBQUs7WUFDakRDLGlCQUFpQnRELE9BQU8sSUFBR2tCLG1CQUFBQSxLQUFLcUIsU0FBUyxjQUFkckIsdUNBQUFBLGlCQUFnQnFDLElBQUk7UUFDakQ7a0NBQUc7U0FBQ3JDLG1CQUFBQSxLQUFLcUIsU0FBUyxjQUFkckIsdUNBQUFBLGlCQUFnQm1DLEtBQUs7U0FBRW5DLG1CQUFBQSxLQUFLcUIsU0FBUyxjQUFkckIsdUNBQUFBLGlCQUFnQnFDLElBQUk7S0FBQztJQUVoRHZILGdEQUFTQTttQ0FBQztZQUNSeUk7WUFDQTsyQ0FBTztvQkFDTCx1Q0FBdUM7b0JBQ3ZDLElBQUkvQixvQkFBb0IxQyxPQUFPLEVBQUU7d0JBQy9CMEMsb0JBQW9CMUMsT0FBTyxDQUFDMEUsS0FBSztvQkFDbkM7b0JBQ0EsNEJBQTRCO29CQUM1QixJQUFJL0Isb0JBQW9CM0MsT0FBTyxFQUFFO3dCQUMvQjJFLGFBQWFoQyxvQkFBb0IzQyxPQUFPO29CQUMxQztvQkFDQSx5QkFBeUI7b0JBQ3pCLElBQUk0QyxpQkFBaUI1QyxPQUFPLEVBQUU7d0JBQzVCMkUsYUFBYS9CLGlCQUFpQjVDLE9BQU87b0JBQ3ZDO2dCQUNGOztRQUNGO2tDQUFHO1FBQUNrQixLQUFLbEUsRUFBRTtLQUFDO0lBRVosdURBQXVEO0lBQ3ZELE1BQU00SCxvQkFBb0IzSSw2Q0FBTUE7bURBQWEsS0FBTzs7SUFDcEQySSxrQkFBa0I1RSxPQUFPLEdBQUc7WUFDMUJ5QztTQUFBQSwwQkFBQUEsZUFBZXpDLE9BQU8sY0FBdEJ5Qyw4Q0FBQUEsd0JBQXdCb0MsY0FBYyxDQUFDO1lBQUVDLFVBQVU7UUFBUztJQUM5RDtJQUVBLCtEQUErRDtJQUMvRCxNQUFNQyxtQkFBbUI5SSw2Q0FBTUEsQ0FBQ3NGLFNBQVN1QyxNQUFNO0lBQy9DOUgsZ0RBQVNBO21DQUFDO1lBQ1IsSUFBSXVGLFNBQVN1QyxNQUFNLEdBQUdpQixpQkFBaUIvRSxPQUFPLEVBQUU7Z0JBQzlDK0UsaUJBQWlCL0UsT0FBTyxHQUFHdUIsU0FBU3VDLE1BQU07Z0JBQzFDLE1BQU1rQixZQUFZQzt5REFBVzs0QkFDM0JMO3lCQUFBQSw2QkFBQUEsa0JBQWtCNUUsT0FBTyxjQUF6QjRFLGlEQUFBQSxnQ0FBQUE7b0JBQ0Y7d0RBQUc7Z0JBQ0g7K0NBQU8sSUFBTUQsYUFBYUs7O1lBQzVCO1lBQ0FELGlCQUFpQi9FLE9BQU8sR0FBR3VCLFNBQVN1QyxNQUFNO1FBQzVDO2tDQUFHO1FBQUN2QyxTQUFTdUMsTUFBTTtLQUFDO0lBRXBCLDRFQUE0RTtJQUM1RSxNQUFNb0IsaUJBQWlCakosNkNBQU1BLENBQUMwRDtJQUM5QjNELGdEQUFTQTttQ0FBQztZQUNSLElBQUkyRCxlQUFlLENBQUN1RixlQUFlbEYsT0FBTyxFQUFFO2dCQUMxQyx5QkFBeUI7Z0JBQ3pCLE1BQU1nRixZQUFZQzt5REFBVzs0QkFDM0JMO3lCQUFBQSw2QkFBQUEsa0JBQWtCNUUsT0FBTyxjQUF6QjRFLGlEQUFBQSxnQ0FBQUE7b0JBQ0Y7d0RBQUc7Z0JBQ0hNLGVBQWVsRixPQUFPLEdBQUdMO2dCQUN6QjsrQ0FBTyxJQUFNZ0YsYUFBYUs7O1lBQzVCO1lBQ0FFLGVBQWVsRixPQUFPLEdBQUdMO1FBQzNCO2tDQUFHO1FBQUNBO0tBQVk7SUFFaEIsTUFBTThFLGVBQWU7UUFDbkIsSUFBSTtZQUNGL0MsV0FBVztZQUNYLE1BQU15RCxXQUFXLE1BQU0vSSx1REFBV0EsQ0FBQ2dKLGVBQWUsQ0FBQ2xFLEtBQUtsRSxFQUFFLEVBQUU7Z0JBQUVxSSxPQUFPO1lBQUc7WUFDeEU3RCxZQUFZMkQsU0FBU0csSUFBSSxDQUFDQyxPQUFPLEtBQUssK0JBQStCO1FBQ3ZFLEVBQUUsT0FBT3JDLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7UUFDNUMsU0FBVTtZQUNSeEIsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNekIsb0JBQW9CL0Qsa0RBQVdBO3dEQUFDLE9BQU9zSjtZQUMzQyxJQUFJLENBQUNBLFlBQVlyRixJQUFJLE1BQU13QixTQUFTO1lBRXBDLE1BQU04RCxTQUFTLFFBQW1CLE9BQVg5RyxLQUFLK0csR0FBRztZQUMvQjlELFdBQVc7WUFFWCxJQUFJO2dCQUNGLHFDQUFxQztnQkFDckMsTUFBTStELGNBQXVCO29CQUMzQjNJLElBQUl5STtvQkFDSm5JLE1BQU07b0JBQ05ELFNBQVNtSTtvQkFDVEksYUFBYTtvQkFDYjFHLFdBQVcsSUFBSVAsT0FBT2tILFdBQVc7b0JBQ2pDQyxXQUFXLElBQUluSCxPQUFPa0gsV0FBVztnQkFDbkM7Z0JBRUFyRTtvRUFBWXVFLENBQUFBLE9BQVE7K0JBQUlBOzRCQUFNSjt5QkFBWTs7Z0JBRTFDeEMsUUFBUTZDLEdBQUcsQ0FBQyw0QkFBNEI5RSxLQUFLbEUsRUFBRSxFQUFFO29CQUFFc0IsU0FBU2tIO29CQUFhUyxXQUFXO2dCQUFLO2dCQUV6RixzQ0FBc0M7Z0JBQ3RDLE1BQU1kLFdBQVcsTUFBTS9JLHVEQUFXQSxDQUFDOEosV0FBVyxDQUFDaEYsS0FBS2xFLEVBQUUsRUFBRTtvQkFDdERzQixTQUFTa0g7b0JBQ1RTLFdBQVc7Z0JBQ2I7Z0JBRUE5QyxRQUFRNkMsR0FBRyxDQUFDLDhCQUE4QmI7Z0JBRTFDLHlEQUF5RDtnQkFDekQsSUFBSUEsU0FBU25JLEVBQUUsRUFBRTtvQkFDZndFO3dFQUFZdUUsQ0FBQUEsT0FBUUEsS0FBS0ksR0FBRztnRkFBQ0MsQ0FBQUEsTUFDM0JBLElBQUlwSixFQUFFLEtBQUt5SSxTQUFTO3dDQUFFLEdBQUdXLEdBQUc7d0NBQUVwSixJQUFJbUksU0FBU25JLEVBQUU7b0NBQUMsSUFBSW9KOzs7Z0JBRXREO2dCQUVBLDJCQUEyQjtnQkFDM0IsTUFBTUM7WUFFUixFQUFFLE9BQU9uRCxPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsMkJBQTJCQTtnQkFDekMsNkNBQTZDO2dCQUM3QzFCO29FQUFZdUUsQ0FBQUEsT0FBUUEsS0FBS08sTUFBTTs0RUFBQ0YsQ0FBQUEsTUFBT0EsSUFBSXBKLEVBQUUsS0FBS3lJOzs7Z0JBQ2xEYyxNQUFNO1lBQ1IsU0FBVTtnQkFDUjNFLFdBQVc7WUFDYjtRQUNGO3VEQUFHO1FBQUNEO1FBQVNULEtBQUtsRSxFQUFFO0tBQUM7SUFFckIsTUFBTXFKLGlCQUFpQjtRQUNyQmxELFFBQVE2QyxHQUFHLENBQUMsNkJBQTZCOUUsS0FBS2xFLEVBQUU7UUFDaEQ4RSxlQUFlO1FBQ2ZELG9CQUFvQjtRQUVwQiwrQ0FBK0M7UUFDL0MsSUFBSWEsb0JBQW9CMUMsT0FBTyxFQUFFO1lBQy9CMEMsb0JBQW9CMUMsT0FBTyxDQUFDMEUsS0FBSztRQUNuQztRQUNBLElBQUkvQixvQkFBb0IzQyxPQUFPLEVBQUU7WUFDL0IyRSxhQUFhaEMsb0JBQW9CM0MsT0FBTztRQUMxQztRQUNBLElBQUk0QyxpQkFBaUI1QyxPQUFPLEVBQUU7WUFDNUIyRSxhQUFhL0IsaUJBQWlCNUMsT0FBTztRQUN2QztRQUVBLElBQUl3RywwQkFBMEI7UUFFOUIsMkRBQTJEO1FBQzNELElBQUlDLGlCQUFpQjtRQUNyQixNQUFNQyx5QkFBeUIsQ0FBQ3BJO1lBQzlCLE1BQU1vSCxNQUFNL0csS0FBSytHLEdBQUc7WUFDcEIsSUFBSS9DLG9CQUFvQjNDLE9BQU8sRUFBRTtnQkFDL0IyRSxhQUFhaEMsb0JBQW9CM0MsT0FBTztZQUMxQztZQUVBLGtFQUFrRTtZQUNsRSxJQUFJMEYsTUFBTWUsaUJBQWlCLEtBQUs7Z0JBQzlCNUUsb0JBQW9CdkQ7Z0JBQ3BCbUksaUJBQWlCZjtZQUNuQixPQUFPO2dCQUNMLGlDQUFpQztnQkFDakMvQyxvQkFBb0IzQyxPQUFPLEdBQUdpRixXQUFXO29CQUN2Q3BELG9CQUFvQnZEO29CQUNwQm1JLGlCQUFpQjlILEtBQUsrRyxHQUFHO2dCQUMzQixHQUFHO1lBQ0w7UUFDRjtRQUVBLE1BQU1pQixZQUFZLENBQUNyQjtZQUNqQm5DLFFBQVE2QyxHQUFHLENBQUMsdUJBQXVCVjtZQUVuQyxPQUFRQSxLQUFLc0IsS0FBSztnQkFDaEIsS0FBSztvQkFDSHpELFFBQVE2QyxHQUFHLENBQUM7b0JBQ1pRLDBCQUEwQjtvQkFDMUIzRSxvQkFBb0I7b0JBQ3BCO2dCQUNGLEtBQUs7b0JBQ0gyRSwyQkFBMkJsQixLQUFLQSxJQUFJO29CQUNwQ29CLHVCQUF1QkY7b0JBQ3ZCO2dCQUNGLEtBQUs7b0JBQ0hyRCxRQUFRNkMsR0FBRyxDQUFDLG9CQUFvQlYsS0FBS0EsSUFBSTtvQkFDekM7Z0JBQ0YsS0FBSzt3QkFZR0E7b0JBWE5uQyxRQUFRNkMsR0FBRyxDQUFDLGdDQUFnQ1E7b0JBQzVDLHNDQUFzQztvQkFDdEMsSUFBSTdELG9CQUFvQjNDLE9BQU8sRUFBRTt3QkFDL0IyRSxhQUFhaEMsb0JBQW9CM0MsT0FBTztvQkFDMUM7b0JBQ0EsdURBQXVEO29CQUN2RCxJQUFJNEMsaUJBQWlCNUMsT0FBTyxFQUFFO3dCQUM1QjJFLGFBQWEvQixpQkFBaUI1QyxPQUFPO29CQUN2QztvQkFDQSxpQ0FBaUM7b0JBQ2pDLE1BQU02RyxlQUF3Qjt3QkFDNUI3SixJQUFJc0ksRUFBQUEsYUFBQUEsS0FBS0EsSUFBSSxjQUFUQSxpQ0FBQUEsV0FBV3dCLGFBQWEsS0FBSSxPQUFrQixPQUFYbkksS0FBSytHLEdBQUc7d0JBQy9DcEksTUFBTTt3QkFDTkQsU0FBU21KO3dCQUNUWixhQUFhO3dCQUNiMUcsV0FBVyxJQUFJUCxPQUFPa0gsV0FBVzt3QkFDakNDLFdBQVcsSUFBSW5ILE9BQU9rSCxXQUFXO29CQUNuQztvQkFFQXJFLFlBQVl1RSxDQUFBQSxPQUFROytCQUFJQTs0QkFBTWM7eUJBQWE7b0JBQzNDaEYsb0JBQW9CO29CQUNwQkMsZUFBZTtvQkFDZixJQUFJWSxvQkFBb0IxQyxPQUFPLEVBQUU7d0JBQy9CMEMsb0JBQW9CMUMsT0FBTyxDQUFDMEUsS0FBSztvQkFDbkM7b0JBQ0E7Z0JBQ0Y7b0JBQ0V2QixRQUFRNkMsR0FBRyxDQUFDLHNCQUFzQlYsS0FBS3NCLEtBQUs7WUFDaEQ7UUFDRjtRQUVBLE1BQU1HLFVBQVUsT0FBTzdEO2dCQWtCaEJBO1lBakJMQyxRQUFRRCxLQUFLLENBQUMsaUJBQWlCQTtZQUUvQixpREFBaUQ7WUFDakQsSUFBSVAsb0JBQW9CM0MsT0FBTyxFQUFFO2dCQUMvQjJFLGFBQWFoQyxvQkFBb0IzQyxPQUFPO1lBQzFDO1lBQ0EsSUFBSTRDLGlCQUFpQjVDLE9BQU8sRUFBRTtnQkFDNUIyRSxhQUFhL0IsaUJBQWlCNUMsT0FBTztZQUN2QztZQUVBOEIsZUFBZTtZQUNmRCxvQkFBb0I7WUFDcEIsSUFBSWEsb0JBQW9CMUMsT0FBTyxFQUFFO2dCQUMvQjBDLG9CQUFvQjFDLE9BQU8sQ0FBQzBFLEtBQUs7WUFDbkM7WUFFQSxxRkFBcUY7WUFDckYsSUFBSSxHQUFDeEIsaUJBQUFBLE1BQU01RSxPQUFPLGNBQWI0RSxxQ0FBQUEsZUFBZThELFFBQVEsQ0FBQyxlQUFjckgsYUFBYTtnQkFDdER3RCxRQUFRNkMsR0FBRyxDQUFDO2dCQUNaLElBQUk7b0JBQ0YsTUFBTXZCO2dCQUNSLEVBQUUsT0FBT3dDLGFBQWE7b0JBQ3BCOUQsUUFBUUQsS0FBSyxDQUFDLDhCQUE4QitEO2dCQUM1QywrREFBK0Q7Z0JBQ2pFO1lBQ0Y7UUFDRjtRQUVBLElBQUk7WUFDRiwrQkFBK0I7WUFDL0IsTUFBTUMsYUFBYSxNQUFNOUssdURBQVdBLENBQUMrSyxzQkFBc0IsQ0FBQ2pHLEtBQUtsRSxFQUFFLEVBQUUySixXQUFXSTtZQUNoRnJFLG9CQUFvQjFDLE9BQU8sR0FBR2tIO1lBRTlCLDJFQUEyRTtZQUMzRXRFLGlCQUFpQjVDLE9BQU8sR0FBR2lGLFdBQVc7Z0JBQ3BDLDBFQUEwRTtnQkFDMUUsSUFBSXRGLGVBQWUrQyxvQkFBb0IxQyxPQUFPLEVBQUU7b0JBQzlDbUQsUUFBUTZDLEdBQUcsQ0FBQztvQkFDWmUsUUFBUSxJQUFJSyxNQUFNO2dCQUNwQjtZQUNGLEdBQUc7UUFFTCxFQUFFLE9BQU9sRSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyx1Q0FBdUNBO1lBQ3JEcEIsZUFBZTtZQUNmeUUsTUFBTTtRQUNSO0lBQ0Y7SUFFQSxJQUFJOUUsU0FBUztRQUNYLHFCQUNFLDhEQUFDaEU7WUFBSUQsV0FBVTs7OEJBQ2IsOERBQUNDO29CQUFJRCxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNDO2dDQUFJRCxXQUFVOzs7Ozs7MENBQ2YsOERBQUNDO2dDQUFJRCxXQUFVOztrREFDYiw4REFBQ0M7d0NBQUlELFdBQVU7Ozs7OztrREFDZiw4REFBQ0M7d0NBQUlELFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQUlyQiw4REFBQ0M7b0JBQUlELFdBQVU7OEJBQ1o2SixNQUFNQyxJQUFJLENBQUM7d0JBQUV4RCxRQUFRO29CQUFFLEdBQUdxQyxHQUFHLENBQUMsQ0FBQ29CLEdBQUdDLGtCQUNqQyw4REFBQy9KOzRCQUFZRCxXQUFVOzs4Q0FDckIsOERBQUNDO29DQUFJRCxXQUFVOzs7Ozs7OENBQ2YsOERBQUNDO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ0M7NENBQUlELFdBQVU7Ozs7OztzREFDZiw4REFBQ0M7NENBQUlELFdBQVU7Ozs7Ozs7Ozs7Ozs7MkJBSlRnSzs7Ozs7Ozs7Ozs7Ozs7OztJQVdwQjtJQUVBLHFCQUNFLDhEQUFDL0o7UUFBSUQsV0FBVTs7MEJBRWIsOERBQUNDO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ0M7d0JBQUlELFdBQVU7OzRCQUVadUUsWUFBWVosd0JBQ1gsOERBQUM3RSx5REFBTUE7Z0NBQ0xtTCxTQUFRO2dDQUNSQyxNQUFLO2dDQUNMMUcsU0FBU0c7Z0NBQ1QzRCxXQUFVO2dDQUNWbUssY0FBVzswQ0FFWCw0RUFBQy9LLDBHQUFTQTtvQ0FBQ1ksV0FBVTs7Ozs7Ozs7Ozs7MENBSXpCLDhEQUFDQztnQ0FDQ0QsV0FBVywrQkFFVixPQURDdUUsWUFBWVgsa0JBQWtCLDJFQUEyRTtnQ0FFM0dKLFNBQVNlLFlBQVlYLGtCQUFrQkEsa0JBQWtCd0c7O2tEQUV6RCw4REFBQ25LO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ2hCLHlEQUFNQTtnREFBQ2dCLFdBQVU7O2tFQUNoQiw4REFBQ2QsOERBQVdBO3dEQUFDb0MsR0FBRyxHQUFFb0MsbUJBQUFBLEtBQUtxQixTQUFTLGNBQWRyQix1Q0FBQUEsaUJBQWdCbUMsS0FBSzt3REFBRXRFLEdBQUcsR0FBRW1DLG1CQUFBQSxLQUFLcUIsU0FBUyxjQUFkckIsdUNBQUFBLGlCQUFnQnFDLElBQUk7Ozs7OztrRUFDbEUsOERBQUM5RyxpRUFBY0E7d0RBQUNlLFdBQVU7a0VBQ3ZCMEQsRUFBQUEsbUJBQUFBLEtBQUtxQixTQUFTLGNBQWRyQix3Q0FBQUEsdUJBQUFBLGlCQUFnQnFDLElBQUksY0FBcEJyQyw0Q0FBQUEsOEJBQUFBLHFCQUFzQmxDLE1BQU0sQ0FBQyxnQkFBN0JrQyxrREFBQUEsNEJBQWlDakMsV0FBVyxPQUFNOzs7Ozs7Ozs7Ozs7MERBSXZELDhEQUFDeEI7Z0RBQUlELFdBQVU7Ozs7Ozs7Ozs7OztrREFFakIsOERBQUNDOzswREFDQyw4REFBQ29LO2dEQUFHckssV0FBVTswREFBeUIwRCxFQUFBQSxtQkFBQUEsS0FBS3FCLFNBQVMsY0FBZHJCLHVDQUFBQSxpQkFBZ0JxQyxJQUFJLEtBQUk7Ozs7OzswREFDL0QsOERBQUM5RjtnREFBSUQsV0FBVTs7a0VBQ2IsOERBQUM2Qjs7NERBQU02QixLQUFLNEcsWUFBWTs0REFBQzs7Ozs7OztrRUFDekIsOERBQUN6STtrRUFBSzs7Ozs7O2tFQUNOLDhEQUFDQTt3REFBSzdCLFdBQVU7a0VBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTXpDLDhEQUFDQzt3QkFBSUQsV0FBVTs7NEJBRVp1RSxZQUFZWCxpQ0FDWCw4REFBQzlFLHlEQUFNQTtnQ0FDTG1MLFNBQVE7Z0NBQ1JDLE1BQUs7Z0NBQ0wxRyxTQUFTSTtnQ0FDVDVELFdBQVU7Z0NBQ1ZtSyxjQUFXOzBDQUVYLDRFQUFDOUssMEdBQUlBO29DQUFDVyxXQUFVOzs7Ozs7Ozs7OzswQ0FLcEIsOERBQUNsQix5REFBTUE7Z0NBQ0xtTCxTQUFRO2dDQUNSQyxNQUFLO2dDQUNMMUcsU0FBUzZCO2dDQUNUbkQsVUFBVTJDLGNBQWNkLFNBQVN1QyxNQUFNLEtBQUs7Z0NBQzVDdEcsV0FBVTs7a0RBRVYsOERBQUNWLDBHQUFTQTt3Q0FBQ1UsV0FBVTs7Ozs7O29DQUNwQjZFLGFBQWEsZUFBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNbkMsOERBQUM1RTtnQkFDQ0QsV0FBVTtnQkFDVjhCLE9BQU87b0JBQ0x5SSxZQUFZNUYscUJBQ1IsZ0VBQW1GLE9BQW5CQSxvQkFBbUIsT0FDbkY7b0JBQ0o2RixnQkFBZ0I3RixxQkFBcUIsVUFBVTtvQkFDL0M4RixvQkFBb0I5RixxQkFBcUIsV0FBVztvQkFDcEQrRixrQkFBa0IvRixxQkFBcUIsY0FBYztnQkFDdkQ7O29CQUVDWixTQUFTNEUsR0FBRyxDQUFDLENBQUM3SCx3QkFDYiw4REFBQ0Q7NEJBRUNDLFNBQVNBOzRCQUNUQyxnQkFBZ0I2RSxrQkFBa0JwRCxPQUFPOzRCQUN6Q3hCLGVBQWU4RSxpQkFBaUJ0RCxPQUFPOzJCQUhsQzFCLFFBQVF0QixFQUFFOzs7OztvQkFRbEIyQyw2QkFDQyw4REFBQ1I7d0JBQ0NDLGtCQUFrQkE7d0JBQ2xCYixnQkFBZ0I2RSxrQkFBa0JwRCxPQUFPO3dCQUN6Q3hCLGVBQWU4RSxpQkFBaUJ0RCxPQUFPOzs7Ozs7a0NBSTNDLDhEQUFDdkM7d0JBQUltRCxLQUFLNkI7Ozs7Ozs7Ozs7OzswQkFJWiw4REFBQ2pEO2dCQUNDQyxlQUFlUTtnQkFDZlAsVUFBVWlDLFdBQVdoQztnQkFDckJuQixlQUFlOEUsaUJBQWlCdEQsT0FBTztnQkFDdkNMLGFBQWFBOzs7Ozs7Ozs7Ozs7QUFJckI7O0FBRUFzQixjQUFja0gsV0FBVyxHQUFHO0FBRUgiLCJzb3VyY2VzIjpbIkU6XFxiZXNzdGlla3VcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGNoYXRcXGNoYXQtaW50ZXJmYWNlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlUmVmLCB1c2VDYWxsYmFjaywgbWVtbyB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgQ2hhdCwgTWVzc2FnZSwgU1NFRXZlbnQgfSBmcm9tICdAL3R5cGVzL2NoYXQnO1xyXG5pbXBvcnQgeyBDaGFyYWN0ZXJBc3NldCB9IGZyb20gJ0AvdHlwZXMvY2hhcmFjdGVyJztcclxuaW1wb3J0IHsgY2hhdFNlcnZpY2UgfSBmcm9tICdAL3NlcnZpY2VzL2NoYXQnO1xyXG5pbXBvcnQgeyBjaGFyYWN0ZXJTZXJ2aWNlIH0gZnJvbSAnQC9zZXJ2aWNlcy9jaGFyYWN0ZXInO1xyXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJztcclxuaW1wb3J0IHsgSW5wdXQgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvaW5wdXQnO1xyXG5pbXBvcnQgeyBBdmF0YXIsIEF2YXRhckZhbGxiYWNrLCBBdmF0YXJJbWFnZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9hdmF0YXInO1xyXG5pbXBvcnQgeyBTZW5kLCBBcnJvd0xlZnQsIEluZm8sIFJvdGF0ZUNjdyB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XHJcbmltcG9ydCB7IGZvcm1hdERpc3RhbmNlVG9Ob3cgfSBmcm9tICdkYXRlLWZucyc7XHJcbmltcG9ydCB7IGlkIH0gZnJvbSAnZGF0ZS1mbnMvbG9jYWxlJztcclxuaW1wb3J0IFJlYWN0TWFya2Rvd24gZnJvbSAncmVhY3QtbWFya2Rvd24nO1xyXG5pbXBvcnQgcmVtYXJrR2ZtIGZyb20gJ3JlbWFyay1nZm0nO1xyXG5pbXBvcnQgeyB0b2FzdCB9IGZyb20gJ3Nvbm5lcic7XHJcblxyXG5pbnRlcmZhY2UgQ2hhdEludGVyZmFjZVByb3BzIHtcclxuICBjaGF0OiBDaGF0O1xyXG4gIG9uQmFjaz86ICgpID0+IHZvaWQ7XHJcbiAgb25Ub2dnbGVQcm9maWxlPzogKCkgPT4gdm9pZDtcclxuICBvbkNoYXRSZXNldD86ICgpID0+IHZvaWQ7XHJcbiAgc2VsZWN0ZWRCYWNrZ3JvdW5kPzogc3RyaW5nIHwgbnVsbDtcclxufVxyXG5cclxuaW50ZXJmYWNlIE1lc3NhZ2VDb250ZW50UHJvcHMge1xyXG4gIGNvbnRlbnQ6IHN0cmluZztcclxuICByb2xlOiAndXNlcicgfCAnYXNzaXN0YW50JztcclxufVxyXG5cclxuY29uc3QgTWVzc2FnZUNvbnRlbnQgPSBtZW1vKCh7IGNvbnRlbnQsIHJvbGUgfTogTWVzc2FnZUNvbnRlbnRQcm9wcykgPT4ge1xyXG4gIGlmIChyb2xlID09PSAndXNlcicpIHtcclxuICAgIC8vIEZvciB1c2VyIG1lc3NhZ2VzLCBqdXN0IGRpc3BsYXkgYXMgcGxhaW4gdGV4dCB3aXRoIGxpbmUgYnJlYWtzXHJcbiAgICByZXR1cm4gPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB3aGl0ZXNwYWNlLXByZS13cmFwIGxlYWRpbmctcmVsYXhlZFwiPntjb250ZW50fTwvcD47XHJcbiAgfVxyXG5cclxuICAvLyBGb3IgQUkgbWVzc2FnZXMsIHJlbmRlciBhcyBNYXJrZG93blxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gbGVhZGluZy1yZWxheGVkIHByb3NlIHByb3NlLXNtIG1heC13LW5vbmUgZGFyazpwcm9zZS1pbnZlcnRcIj5cclxuICAgICAgPFJlYWN0TWFya2Rvd25cclxuICAgICAgICByZW1hcmtQbHVnaW5zPXtbcmVtYXJrR2ZtXX1cclxuICAgICAgICBjb21wb25lbnRzPXt7XHJcbiAgICAgICAgICAvLyBDdXN0b21pemUgcGFyYWdyYXBoIHN0eWxpbmdcclxuICAgICAgICAgIHA6ICh7IGNoaWxkcmVuIH0pID0+IChcclxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibWItMiBsYXN0Om1iLTAgbGVhZGluZy1yZWxheGVkXCI+e2NoaWxkcmVufTwvcD5cclxuICAgICAgICAgICksXHJcbiAgICAgICAgICAvLyBDdXN0b21pemUgZW1waGFzaXMgKGl0YWxpYylcclxuICAgICAgICAgIGVtOiAoeyBjaGlsZHJlbiB9KSA9PiAoXHJcbiAgICAgICAgICAgIDxlbSBjbGFzc05hbWU9XCJpdGFsaWMgdGV4dC1pbmhlcml0XCI+e2NoaWxkcmVufTwvZW0+XHJcbiAgICAgICAgICApLFxyXG4gICAgICAgICAgLy8gQ3VzdG9taXplIHN0cm9uZyAoYm9sZClcclxuICAgICAgICAgIHN0cm9uZzogKHsgY2hpbGRyZW4gfSkgPT4gKFxyXG4gICAgICAgICAgICA8c3Ryb25nIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1pbmhlcml0XCI+e2NoaWxkcmVufTwvc3Ryb25nPlxyXG4gICAgICAgICAgKSxcclxuICAgICAgICAgIC8vIEN1c3RvbWl6ZSBjb2RlIGJsb2Nrc1xyXG4gICAgICAgICAgY29kZTogKHsgY2hpbGRyZW4sIGNsYXNzTmFtZSB9KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IGlzSW5saW5lID0gIWNsYXNzTmFtZTtcclxuICAgICAgICAgICAgaWYgKGlzSW5saW5lKSB7XHJcbiAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgIDxjb2RlIGNsYXNzTmFtZT1cImJnLW11dGVkIHB4LTEuNSBweS0wLjUgcm91bmRlZCB0ZXh0LXhzIGZvbnQtbW9ub1wiPlxyXG4gICAgICAgICAgICAgICAgICB7Y2hpbGRyZW59XHJcbiAgICAgICAgICAgICAgICA8L2NvZGU+XHJcbiAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgIDxjb2RlIGNsYXNzTmFtZT1cImJsb2NrIGJnLW11dGVkIHAtMyByb3VuZGVkLWxnIHRleHQteHMgZm9udC1tb25vIG92ZXJmbG93LXgtYXV0b1wiPlxyXG4gICAgICAgICAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICAgICAgICAgIDwvY29kZT5cclxuICAgICAgICAgICAgKTtcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgICAvLyBDdXN0b21pemUgbGlzdHNcclxuICAgICAgICAgIHVsOiAoeyBjaGlsZHJlbiB9KSA9PiAoXHJcbiAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJsaXN0LWRpc2MgbGlzdC1pbnNpZGUgbWItMiBzcGFjZS15LTFcIj57Y2hpbGRyZW59PC91bD5cclxuICAgICAgICAgICksXHJcbiAgICAgICAgICBvbDogKHsgY2hpbGRyZW4gfSkgPT4gKFxyXG4gICAgICAgICAgICA8b2wgY2xhc3NOYW1lPVwibGlzdC1kZWNpbWFsIGxpc3QtaW5zaWRlIG1iLTIgc3BhY2UteS0xXCI+e2NoaWxkcmVufTwvb2w+XHJcbiAgICAgICAgICApLFxyXG4gICAgICAgICAgbGk6ICh7IGNoaWxkcmVuIH0pID0+IChcclxuICAgICAgICAgICAgPGxpIGNsYXNzTmFtZT1cInRleHQtc21cIj57Y2hpbGRyZW59PC9saT5cclxuICAgICAgICAgICksXHJcbiAgICAgICAgICAvLyBDdXN0b21pemUgYmxvY2txdW90ZXNcclxuICAgICAgICAgIGJsb2NrcXVvdGU6ICh7IGNoaWxkcmVuIH0pID0+IChcclxuICAgICAgICAgICAgPGJsb2NrcXVvdGUgY2xhc3NOYW1lPVwiYm9yZGVyLWwtNCBib3JkZXItbXV0ZWQgcGwtNCBpdGFsaWMgbXktMlwiPlxyXG4gICAgICAgICAgICAgIHtjaGlsZHJlbn1cclxuICAgICAgICAgICAgPC9ibG9ja3F1b3RlPlxyXG4gICAgICAgICAgKSxcclxuICAgICAgICB9fVxyXG4gICAgICA+XHJcbiAgICAgICAge2NvbnRlbnR9XHJcbiAgICAgIDwvUmVhY3RNYXJrZG93bj5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn0pO1xyXG5cclxuaW50ZXJmYWNlIE1lc3NhZ2VJdGVtUHJvcHMge1xyXG4gIG1lc3NhZ2U6IE1lc3NhZ2U7XHJcbiAgY2hhcmFjdGVySW1hZ2U/OiBzdHJpbmc7XHJcbiAgY2hhcmFjdGVyTmFtZT86IHN0cmluZztcclxufVxyXG5cclxuY29uc3QgTWVzc2FnZUl0ZW0gPSBtZW1vKCh7IG1lc3NhZ2UsIGNoYXJhY3RlckltYWdlLCBjaGFyYWN0ZXJOYW1lIH06IE1lc3NhZ2VJdGVtUHJvcHMpID0+IHtcclxuICBjb25zdCBmb3JtYXRUaW1lID0gdXNlQ2FsbGJhY2soKGRhdGVTdHJpbmc6IHN0cmluZykgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgcmV0dXJuIGZvcm1hdERpc3RhbmNlVG9Ob3cobmV3IERhdGUoZGF0ZVN0cmluZyksIHsgYWRkU3VmZml4OiB0cnVlLCBsb2NhbGU6IGlkIH0pO1xyXG4gICAgfSBjYXRjaCB7XHJcbiAgICAgIHJldHVybiAnJztcclxuICAgIH1cclxuICB9LCBbXSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2XHJcbiAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtZW5kIGdhcC0yICR7bWVzc2FnZS5yb2xlID09PSAndXNlcicgPyAnanVzdGlmeS1lbmQnIDogJ2p1c3RpZnktc3RhcnQnfWB9XHJcbiAgICA+XHJcbiAgICAgIHsvKiBDaGFyYWN0ZXIgQXZhdGFyIGZvciBhc3Npc3RhbnQgbWVzc2FnZXMgKi99XHJcbiAgICAgIHttZXNzYWdlLnJvbGUgPT09ICdhc3Npc3RhbnQnICYmIChcclxuICAgICAgICA8QXZhdGFyIGNsYXNzTmFtZT1cInctOCBoLTggbWItMVwiPlxyXG4gICAgICAgICAgPEF2YXRhckltYWdlIHNyYz17Y2hhcmFjdGVySW1hZ2V9IGFsdD17Y2hhcmFjdGVyTmFtZX0gLz5cclxuICAgICAgICAgIDxBdmF0YXJGYWxsYmFjayBjbGFzc05hbWU9XCJ0ZXh0LXhzIGJnLWJlc3RpZWt1LXByaW1hcnkvMTAgdGV4dC1iZXN0aWVrdS1wcmltYXJ5XCI+XHJcbiAgICAgICAgICAgIHtjaGFyYWN0ZXJOYW1lPy5jaGFyQXQoMCk/LnRvVXBwZXJDYXNlKCkgfHwgJ0MnfVxyXG4gICAgICAgICAgPC9BdmF0YXJGYWxsYmFjaz5cclxuICAgICAgICA8L0F2YXRhcj5cclxuICAgICAgKX1cclxuXHJcbiAgICAgIDxkaXZcclxuICAgICAgICBjbGFzc05hbWU9e2BtYXgtdy1bNzAlXSByb3VuZGVkLTJ4bCBwLTQgc2hhZG93LXNtICR7XHJcbiAgICAgICAgICBtZXNzYWdlLnJvbGUgPT09ICd1c2VyJ1xyXG4gICAgICAgICAgICA/ICdiZy1iZXN0aWVrdS1wcmltYXJ5IHRleHQtd2hpdGUgcm91bmRlZC1ici1tZCdcclxuICAgICAgICAgICAgOiAnYmctd2hpdGUgZGFyazpiZy1tdXRlZCBib3JkZXIgcm91bmRlZC1ibC1tZCdcclxuICAgICAgICB9YH1cclxuICAgICAgPlxyXG4gICAgICAgIDxNZXNzYWdlQ29udGVudCBjb250ZW50PXttZXNzYWdlLmNvbnRlbnR9IHJvbGU9e21lc3NhZ2Uucm9sZX0gLz5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtdC0yICR7XHJcbiAgICAgICAgICBtZXNzYWdlLnJvbGUgPT09ICd1c2VyJyA/ICd0ZXh0LXdoaXRlLzcwJyA6ICd0ZXh0LW11dGVkLWZvcmVncm91bmQnXHJcbiAgICAgICAgfWB9PlxyXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14c1wiPlxyXG4gICAgICAgICAgICB7Zm9ybWF0VGltZShtZXNzYWdlLmNyZWF0ZWRBdCl9XHJcbiAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICB7bWVzc2FnZS5yb2xlID09PSAndXNlcicgJiYgKFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEgaC0xIGJnLXdoaXRlLzcwIHJvdW5kZWQtZnVsbFwiPjwvZGl2PlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xIGgtMSBiZy13aGl0ZS83MCByb3VuZGVkLWZ1bGxcIj48L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICApfVxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn0pO1xyXG5cclxuaW50ZXJmYWNlIFN0cmVhbWluZ01lc3NhZ2VQcm9wcyB7XHJcbiAgc3RyZWFtaW5nTWVzc2FnZTogc3RyaW5nO1xyXG4gIGNoYXJhY3RlckltYWdlPzogc3RyaW5nO1xyXG4gIGNoYXJhY3Rlck5hbWU/OiBzdHJpbmc7XHJcbn1cclxuXHJcbmNvbnN0IFN0cmVhbWluZ01lc3NhZ2UgPSBtZW1vKCh7IHN0cmVhbWluZ01lc3NhZ2UsIGNoYXJhY3RlckltYWdlLCBjaGFyYWN0ZXJOYW1lIH06IFN0cmVhbWluZ01lc3NhZ2VQcm9wcykgPT4ge1xyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtZW5kIGdhcC0yIGp1c3RpZnktc3RhcnRcIj5cclxuICAgICAgPEF2YXRhciBjbGFzc05hbWU9XCJ3LTggaC04IG1iLTFcIj5cclxuICAgICAgICA8QXZhdGFySW1hZ2Ugc3JjPXtjaGFyYWN0ZXJJbWFnZX0gYWx0PXtjaGFyYWN0ZXJOYW1lfSAvPlxyXG4gICAgICAgIDxBdmF0YXJGYWxsYmFjayBjbGFzc05hbWU9XCJ0ZXh0LXhzIGJnLWJlc3RpZWt1LXByaW1hcnkvMTAgdGV4dC1iZXN0aWVrdS1wcmltYXJ5XCI+XHJcbiAgICAgICAgICB7Y2hhcmFjdGVyTmFtZT8uY2hhckF0KDApPy50b1VwcGVyQ2FzZSgpIHx8ICdDJ31cclxuICAgICAgICA8L0F2YXRhckZhbGxiYWNrPlxyXG4gICAgICA8L0F2YXRhcj5cclxuXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctWzcwJV0gcm91bmRlZC0yeGwgcm91bmRlZC1ibC1tZCBwLTQgYmctd2hpdGUgZGFyazpiZy1tdXRlZCBib3JkZXIgc2hhZG93LXNtXCI+XHJcbiAgICAgICAge3N0cmVhbWluZ01lc3NhZ2UgPyAoXHJcbiAgICAgICAgICA8TWVzc2FnZUNvbnRlbnQgY29udGVudD17c3RyZWFtaW5nTWVzc2FnZX0gcm9sZT1cImFzc2lzdGFudFwiIC8+XHJcbiAgICAgICAgKSA6IChcclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cclxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5CZXJwaWtpcjwvc3Bhbj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMSBtbC0yXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLWJlc3RpZWt1LXByaW1hcnkgcm91bmRlZC1mdWxsIGFuaW1hdGUtYm91bmNlXCIgLz5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctYmVzdGlla3UtcHJpbWFyeSByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1ib3VuY2VcIiBzdHlsZT17eyBhbmltYXRpb25EZWxheTogJzAuMXMnIH19IC8+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLWJlc3RpZWt1LXByaW1hcnkgcm91bmRlZC1mdWxsIGFuaW1hdGUtYm91bmNlXCIgc3R5bGU9e3sgYW5pbWF0aW9uRGVsYXk6ICcwLjJzJyB9fSAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICl9XHJcblxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgbXQtMiB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEgaC0xIGJnLWJlc3RpZWt1LXByaW1hcnkgcm91bmRlZC1mdWxsIGFuaW1hdGUtcHVsc2VcIj48L2Rpdj5cclxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14c1wiPk1lbmdldGlrLi4uPC9zcGFuPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufSk7XHJcblxyXG5pbnRlcmZhY2UgQ2hhdElucHV0UHJvcHMge1xyXG4gIG9uU2VuZE1lc3NhZ2U6IChtZXNzYWdlOiBzdHJpbmcpID0+IHZvaWQ7XHJcbiAgZGlzYWJsZWQ6IGJvb2xlYW47XHJcbiAgY2hhcmFjdGVyTmFtZT86IHN0cmluZztcclxuICBpc1N0cmVhbWluZzogYm9vbGVhbjtcclxufVxyXG5cclxuY29uc3QgQ2hhdElucHV0ID0gbWVtbygoeyBvblNlbmRNZXNzYWdlLCBkaXNhYmxlZCwgY2hhcmFjdGVyTmFtZSwgaXNTdHJlYW1pbmcgfTogQ2hhdElucHV0UHJvcHMpID0+IHtcclxuICBjb25zdCBbbmV3TWVzc2FnZSwgc2V0TmV3TWVzc2FnZV0gPSB1c2VTdGF0ZSgnJyk7XHJcbiAgY29uc3QgaW5wdXRSZWYgPSB1c2VSZWY8SFRNTElucHV0RWxlbWVudD4obnVsbCk7XHJcblxyXG4gIC8vIFVzZSByZWYgdG8gc3RvcmUgY3VycmVudCBtZXNzYWdlIHRvIGF2b2lkIHN0YWxlIGNsb3N1cmVzXHJcbiAgY29uc3QgbWVzc2FnZVJlZiA9IHVzZVJlZignJyk7XHJcbiAgbWVzc2FnZVJlZi5jdXJyZW50ID0gbmV3TWVzc2FnZTtcclxuXHJcbiAgY29uc3QgaGFuZGxlU2VuZE1lc3NhZ2UgPSB1c2VDYWxsYmFjaygoKSA9PiB7XHJcbiAgICBjb25zdCBjdXJyZW50TWVzc2FnZSA9IG1lc3NhZ2VSZWYuY3VycmVudC50cmltKCk7XHJcbiAgICBpZiAoIWN1cnJlbnRNZXNzYWdlIHx8IGRpc2FibGVkKSByZXR1cm47XHJcbiAgICBvblNlbmRNZXNzYWdlKGN1cnJlbnRNZXNzYWdlKTtcclxuICAgIHNldE5ld01lc3NhZ2UoJycpO1xyXG4gICAgbWVzc2FnZVJlZi5jdXJyZW50ID0gJyc7XHJcbiAgfSwgW2Rpc2FibGVkLCBvblNlbmRNZXNzYWdlXSk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZUtleVByZXNzID0gdXNlQ2FsbGJhY2soKGU6IFJlYWN0LktleWJvYXJkRXZlbnQpID0+IHtcclxuICAgIGlmIChlLmtleSA9PT0gJ0VudGVyJyAmJiAhZS5zaGlmdEtleSkge1xyXG4gICAgICBlLnByZXZlbnREZWZhdWx0KCk7XHJcbiAgICAgIGhhbmRsZVNlbmRNZXNzYWdlKCk7XHJcbiAgICB9XHJcbiAgfSwgW2hhbmRsZVNlbmRNZXNzYWdlXSk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZUlucHV0Q2hhbmdlID0gdXNlQ2FsbGJhY2soKGU6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiB7XHJcbiAgICBjb25zdCB2YWx1ZSA9IGUudGFyZ2V0LnZhbHVlO1xyXG4gICAgc2V0TmV3TWVzc2FnZSh2YWx1ZSk7XHJcbiAgICBtZXNzYWdlUmVmLmN1cnJlbnQgPSB2YWx1ZTtcclxuICB9LCBbXSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTAgYm9yZGVyLXQgcC00IGJnLWJhY2tncm91bmQvOTUgYmFja2Ryb3AtYmx1ciBzdXBwb3J0cy1bYmFja2Ryb3AtZmlsdGVyXTpiZy1iYWNrZ3JvdW5kLzYwXCI+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1lbmQgc3BhY2UteC0zXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgcmVsYXRpdmVcIj5cclxuICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICByZWY9e2lucHV0UmVmfVxyXG4gICAgICAgICAgICB2YWx1ZT17bmV3TWVzc2FnZX1cclxuICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxyXG4gICAgICAgICAgICBvbktleVByZXNzPXtoYW5kbGVLZXlQcmVzc31cclxuICAgICAgICAgICAgcGxhY2Vob2xkZXI9e2BNZXNzYWdlICR7Y2hhcmFjdGVyTmFtZSB8fCAnY2hhcmFjdGVyJ30uLi5gfVxyXG4gICAgICAgICAgICBkaXNhYmxlZD17ZGlzYWJsZWR9XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInByLTEyIHB5LTMgcm91bmRlZC0yeGwgYm9yZGVyLTIgZm9jdXM6Ym9yZGVyLWJlc3RpZWt1LXByaW1hcnkgdHJhbnNpdGlvbi1jb2xvcnNcIlxyXG4gICAgICAgICAgLz5cclxuICAgICAgICAgIHsvKiBDaGFyYWN0ZXIgY291bnQgb3Igc3RhdHVzICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSByaWdodC0zIHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteS0xLzJcIj5cclxuICAgICAgICAgICAge2Rpc2FibGVkICYmIChcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNCBoLTQgYm9yZGVyLTIgYm9yZGVyLWJlc3RpZWt1LXByaW1hcnkgYm9yZGVyLXQtdHJhbnNwYXJlbnQgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpblwiPjwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIDxCdXR0b25cclxuICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNlbmRNZXNzYWdlfVxyXG4gICAgICAgICAgZGlzYWJsZWQ9eyFuZXdNZXNzYWdlLnRyaW0oKSB8fCBkaXNhYmxlZH1cclxuICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJlc3RpZWt1LXByaW1hcnkgaG92ZXI6YmctYmVzdGlla3UtcHJpbWFyeS1kYXJrIHJvdW5kZWQtMnhsIHB4LTYgcHktMyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgaG92ZXI6c2NhbGUtMTA1IGRpc2FibGVkOmhvdmVyOnNjYWxlLTEwMFwiXHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgPFNlbmQgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XHJcbiAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgey8qIFF1aWNrIGFjdGlvbnMgb3Igc3VnZ2VzdGlvbnMgKi99XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG10LTIgdGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cclxuICAgICAgICA8c3Bhbj5UZWthbiBFbnRlciB1bnR1ayBtZW5naXJpbSwgU2hpZnQrRW50ZXIgdW50dWsgYmFyaXMgYmFydTwvc3Bhbj5cclxuICAgICAgICB7aXNTdHJlYW1pbmcgJiYgKFxyXG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1iZXN0aWVrdS1wcmltYXJ5IGFuaW1hdGUtcHVsc2VcIj5BSSBzZWRhbmcgbWVyZXNwb25zLi4uPC9zcGFuPlxyXG4gICAgICAgICl9XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufSk7XHJcblxyXG5jb25zdCBDaGF0SW50ZXJmYWNlID0gbWVtbygoeyBjaGF0LCBvbkJhY2ssIG9uVG9nZ2xlUHJvZmlsZSwgb25DaGF0UmVzZXQsIHNlbGVjdGVkQmFja2dyb3VuZCB9OiBDaGF0SW50ZXJmYWNlUHJvcHMpID0+IHtcclxuICBjb25zdCBbbWVzc2FnZXMsIHNldE1lc3NhZ2VzXSA9IHVzZVN0YXRlPE1lc3NhZ2VbXT4oW10pO1xyXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xyXG4gIGNvbnN0IFtzZW5kaW5nLCBzZXRTZW5kaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbc3RyZWFtaW5nTWVzc2FnZSwgc2V0U3RyZWFtaW5nTWVzc2FnZV0gPSB1c2VTdGF0ZSgnJyk7XHJcbiAgY29uc3QgW2lzU3RyZWFtaW5nLCBzZXRJc1N0cmVhbWluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2lzTW9iaWxlLCBzZXRJc01vYmlsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2NoYXJhY3RlckFzc2V0cywgc2V0Q2hhcmFjdGVyQXNzZXRzXSA9IHVzZVN0YXRlPENoYXJhY3RlckFzc2V0W10+KFtdKTtcclxuICBjb25zdCBbYmFja2dyb3VuZEFzc2V0VXJsLCBzZXRCYWNrZ3JvdW5kQXNzZXRVcmxdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW2lzRGVsZXRpbmcsIHNldElzRGVsZXRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtjaGFyYWN0ZXIsIHNldENoYXJhY3Rlcl0gPSB1c2VTdGF0ZTxhbnk+KG51bGwpO1xyXG4gIGNvbnN0IG1lc3NhZ2VzRW5kUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcclxuICBjb25zdCBzdHJlYW1Db25uZWN0aW9uUmVmID0gdXNlUmVmPHsgY2xvc2U6ICgpID0+IHZvaWQgfSB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IHN0cmVhbWluZ1RpbWVvdXRSZWYgPSB1c2VSZWY8Tm9kZUpTLlRpbWVvdXQgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBzdHJlYW1UaW1lb3V0UmVmID0gdXNlUmVmPE5vZGVKUy5UaW1lb3V0IHwgbnVsbD4obnVsbCk7XHJcblxyXG4gIC8vIERlbGV0ZSBjaGF0IG1lc3NhZ2VzIGZ1bmN0aW9uXHJcbiAgY29uc3QgaGFuZGxlRGVsZXRlTWVzc2FnZXMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICBpZiAoIWNoYXQuaWQpIHJldHVybjtcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBzZXRJc0RlbGV0aW5nKHRydWUpO1xyXG4gICAgICBhd2FpdCBjaGF0U2VydmljZS5kZWxldGVDaGF0TWVzc2FnZXMoY2hhdC5pZCk7XHJcblxyXG4gICAgICAvLyBDbGVhciBtZXNzYWdlcyBsb2NhbGx5XHJcbiAgICAgIHNldE1lc3NhZ2VzKFtdKTtcclxuXHJcbiAgICAgIC8vIFNob3cgc3VjY2VzcyB0b2FzdFxyXG4gICAgICB0b2FzdC5zdWNjZXNzKFwiQ2hhdCBiZXJoYXNpbCBkaXJlc2V0IVwiLCB7XHJcbiAgICAgICAgZGVzY3JpcHRpb246IFwiU2VtdWEgcGVzYW4gdGVsYWggZGloYXB1cy4gQW5kYSBiaXNhIG1lbXVsYWkgcGVyY2FrYXBhbiBiYXJ1LlwiLFxyXG4gICAgICAgIGR1cmF0aW9uOiA0MDAwLFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIC8vIENhbGwgcGFyZW50IGNhbGxiYWNrIGlmIHByb3ZpZGVkXHJcbiAgICAgIGlmIChvbkNoYXRSZXNldCkge1xyXG4gICAgICAgIG9uQ2hhdFJlc2V0KCk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBkZWxldGUgbWVzc2FnZXM6JywgZXJyb3IpO1xyXG4gICAgICB0b2FzdC5lcnJvcihcIkdhZ2FsIG1lcmVzZXQgY2hhdFwiLCB7XHJcbiAgICAgICAgZGVzY3JpcHRpb246IFwiVGVyamFkaSBrZXNhbGFoYW4gc2FhdCBtZW5naGFwdXMgcGVzYW4uIFNpbGFrYW4gY29iYSBsYWdpLlwiLFxyXG4gICAgICAgIGR1cmF0aW9uOiA0MDAwLFxyXG4gICAgICB9KTtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldElzRGVsZXRpbmcoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIFN0YWJsZSByZWZlcmVuY2VzIGZvciBjaGFyYWN0ZXIgZGF0YSB0byBwcmV2ZW50IHVubmVjZXNzYXJ5IHJlLXJlbmRlcnNcclxuICBjb25zdCBjaGFyYWN0ZXJJbWFnZVJlZiA9IHVzZVJlZihjaGF0LmNoYXJhY3Rlcj8uaW1hZ2UpO1xyXG4gIGNvbnN0IGNoYXJhY3Rlck5hbWVSZWYgPSB1c2VSZWYoY2hhdC5jaGFyYWN0ZXI/Lm5hbWUpO1xyXG5cclxuICAvLyBMb2FkIGNoYXJhY3RlciBkYXRhIGFuZCBhc3NldHNcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgbG9hZENoYXJhY3RlckRhdGEgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgLy8gTG9hZCBjaGFyYWN0ZXIgZGV0YWlsc1xyXG4gICAgICAgIGNvbnN0IGNoYXJhY3RlckRhdGEgPSBhd2FpdCBjaGFyYWN0ZXJTZXJ2aWNlLmdldENoYXJhY3RlckJ5SWQoY2hhdC5jaGFyYWN0ZXJJZCk7XHJcbiAgICAgICAgc2V0Q2hhcmFjdGVyKGNoYXJhY3RlckRhdGEpO1xyXG5cclxuICAgICAgICAvLyBMb2FkIGNoYXJhY3RlciBhc3NldHNcclxuICAgICAgICBjb25zdCBhc3NldHMgPSBhd2FpdCBjaGFyYWN0ZXJTZXJ2aWNlLmdldENoYXJhY3RlckFzc2V0cyhjaGF0LmNoYXJhY3RlcklkKTtcclxuICAgICAgICBzZXRDaGFyYWN0ZXJBc3NldHMoYXNzZXRzKTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBjaGFyYWN0ZXIgZGF0YTonLCBlcnJvcik7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgbG9hZENoYXJhY3RlckRhdGEoKTtcclxuICB9LCBbY2hhdC5jaGFyYWN0ZXJJZF0pO1xyXG5cclxuICAvLyBVcGRhdGUgYmFja2dyb3VuZCBVUkwgd2hlbiBzZWxlY3RlZEJhY2tncm91bmQgY2hhbmdlc1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoc2VsZWN0ZWRCYWNrZ3JvdW5kICYmIGNoYXJhY3RlckFzc2V0cy5sZW5ndGggPiAwKSB7XHJcbiAgICAgIGNvbnN0IGJhY2tncm91bmRBc3NldCA9IGNoYXJhY3RlckFzc2V0cy5maW5kKFxyXG4gICAgICAgIGFzc2V0ID0+IGFzc2V0LmlkID09PSBzZWxlY3RlZEJhY2tncm91bmQgJiYgYXNzZXQucHVycG9zZSA9PT0gJ2JhY2tncm91bmQnXHJcbiAgICAgICk7XHJcbiAgICAgIHNldEJhY2tncm91bmRBc3NldFVybChiYWNrZ3JvdW5kQXNzZXQ/LnVybCB8fCBudWxsKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHNldEJhY2tncm91bmRBc3NldFVybChudWxsKTtcclxuICAgIH1cclxuICB9LCBbc2VsZWN0ZWRCYWNrZ3JvdW5kLCBjaGFyYWN0ZXJBc3NldHNdKTtcclxuXHJcbiAgLy8gRGV0ZWN0IG1vYmlsZSBzY3JlZW4gc2l6ZVxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBjaGVja01vYmlsZSA9ICgpID0+IHtcclxuICAgICAgc2V0SXNNb2JpbGUod2luZG93LmlubmVyV2lkdGggPCA3NjgpO1xyXG4gICAgfTtcclxuXHJcbiAgICBjaGVja01vYmlsZSgpO1xyXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIGNoZWNrTW9iaWxlKTtcclxuICAgIHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigncmVzaXplJywgY2hlY2tNb2JpbGUpO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgLy8gVXBkYXRlIHJlZnMgd2hlbiBjaGF0IGNoYW5nZXNcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY2hhcmFjdGVySW1hZ2VSZWYuY3VycmVudCA9IGNoYXQuY2hhcmFjdGVyPy5pbWFnZTtcclxuICAgIGNoYXJhY3Rlck5hbWVSZWYuY3VycmVudCA9IGNoYXQuY2hhcmFjdGVyPy5uYW1lO1xyXG4gIH0sIFtjaGF0LmNoYXJhY3Rlcj8uaW1hZ2UsIGNoYXQuY2hhcmFjdGVyPy5uYW1lXSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBsb2FkTWVzc2FnZXMoKTtcclxuICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgIC8vIENsZWFudXAgc3RyZWFtIGNvbm5lY3Rpb24gb24gdW5tb3VudFxyXG4gICAgICBpZiAoc3RyZWFtQ29ubmVjdGlvblJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgc3RyZWFtQ29ubmVjdGlvblJlZi5jdXJyZW50LmNsb3NlKCk7XHJcbiAgICAgIH1cclxuICAgICAgLy8gQ2xlYW51cCBzdHJlYW1pbmcgdGltZW91dFxyXG4gICAgICBpZiAoc3RyZWFtaW5nVGltZW91dFJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgY2xlYXJUaW1lb3V0KHN0cmVhbWluZ1RpbWVvdXRSZWYuY3VycmVudCk7XHJcbiAgICAgIH1cclxuICAgICAgLy8gQ2xlYW51cCBzdHJlYW0gdGltZW91dFxyXG4gICAgICBpZiAoc3RyZWFtVGltZW91dFJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgY2xlYXJUaW1lb3V0KHN0cmVhbVRpbWVvdXRSZWYuY3VycmVudCk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcbiAgfSwgW2NoYXQuaWRdKTtcclxuXHJcbiAgLy8gU3RhYmxlIHNjcm9sbCBmdW5jdGlvbiB0aGF0IGRvZXNuJ3QgY2F1c2UgcmUtcmVuZGVyc1xyXG4gIGNvbnN0IHNjcm9sbFRvQm90dG9tUmVmID0gdXNlUmVmPCgpID0+IHZvaWQ+KCgpID0+IHt9KTtcclxuICBzY3JvbGxUb0JvdHRvbVJlZi5jdXJyZW50ID0gKCkgPT4ge1xyXG4gICAgbWVzc2FnZXNFbmRSZWYuY3VycmVudD8uc2Nyb2xsSW50b1ZpZXcoeyBiZWhhdmlvcjogJ3Ntb290aCcgfSk7XHJcbiAgfTtcclxuXHJcbiAgLy8gT25seSBzY3JvbGwgd2hlbiBtZXNzYWdlcyBjb3VudCBjaGFuZ2VzLCBub3Qgb24gZXZlcnkgcmVuZGVyXHJcbiAgY29uc3QgbWVzc2FnZXNDb3VudFJlZiA9IHVzZVJlZihtZXNzYWdlcy5sZW5ndGgpO1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAobWVzc2FnZXMubGVuZ3RoID4gbWVzc2FnZXNDb3VudFJlZi5jdXJyZW50KSB7XHJcbiAgICAgIG1lc3NhZ2VzQ291bnRSZWYuY3VycmVudCA9IG1lc3NhZ2VzLmxlbmd0aDtcclxuICAgICAgY29uc3QgdGltZW91dElkID0gc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgc2Nyb2xsVG9Cb3R0b21SZWYuY3VycmVudD8uKCk7XHJcbiAgICAgIH0sIDEwMCk7XHJcbiAgICAgIHJldHVybiAoKSA9PiBjbGVhclRpbWVvdXQodGltZW91dElkKTtcclxuICAgIH1cclxuICAgIG1lc3NhZ2VzQ291bnRSZWYuY3VycmVudCA9IG1lc3NhZ2VzLmxlbmd0aDtcclxuICB9LCBbbWVzc2FnZXMubGVuZ3RoXSk7XHJcblxyXG4gIC8vIFNlcGFyYXRlIGVmZmVjdCBmb3Igc3RyZWFtaW5nIC0gb25seSBzY3JvbGwgd2hlbiBzdHJlYW1pbmcgc3RhcnRzIG9yIGVuZHNcclxuICBjb25zdCBpc1N0cmVhbWluZ1JlZiA9IHVzZVJlZihpc1N0cmVhbWluZyk7XHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChpc1N0cmVhbWluZyAmJiAhaXNTdHJlYW1pbmdSZWYuY3VycmVudCkge1xyXG4gICAgICAvLyBTdHJlYW1pbmcganVzdCBzdGFydGVkXHJcbiAgICAgIGNvbnN0IHRpbWVvdXRJZCA9IHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgIHNjcm9sbFRvQm90dG9tUmVmLmN1cnJlbnQ/LigpO1xyXG4gICAgICB9LCAyMDApO1xyXG4gICAgICBpc1N0cmVhbWluZ1JlZi5jdXJyZW50ID0gaXNTdHJlYW1pbmc7XHJcbiAgICAgIHJldHVybiAoKSA9PiBjbGVhclRpbWVvdXQodGltZW91dElkKTtcclxuICAgIH1cclxuICAgIGlzU3RyZWFtaW5nUmVmLmN1cnJlbnQgPSBpc1N0cmVhbWluZztcclxuICB9LCBbaXNTdHJlYW1pbmddKTtcclxuXHJcbiAgY29uc3QgbG9hZE1lc3NhZ2VzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBjaGF0U2VydmljZS5nZXRDaGF0TWVzc2FnZXMoY2hhdC5pZCwgeyBsaW1pdDogNTAgfSk7XHJcbiAgICAgIHNldE1lc3NhZ2VzKHJlc3BvbnNlLmRhdGEucmV2ZXJzZSgpKTsgLy8gUmV2ZXJzZSB0byBzaG93IG9sZGVzdCBmaXJzdFxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGxvYWQgbWVzc2FnZXM6JywgZXJyb3IpO1xyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlU2VuZE1lc3NhZ2UgPSB1c2VDYWxsYmFjayhhc3luYyAobWVzc2FnZVRleHQ6IHN0cmluZykgPT4ge1xyXG4gICAgaWYgKCFtZXNzYWdlVGV4dC50cmltKCkgfHwgc2VuZGluZykgcmV0dXJuO1xyXG5cclxuICAgIGNvbnN0IHRlbXBJZCA9IGB0ZW1wLSR7RGF0ZS5ub3coKX1gO1xyXG4gICAgc2V0U2VuZGluZyh0cnVlKTtcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICAvLyBBZGQgdXNlciBtZXNzYWdlIHRvIFVJIGltbWVkaWF0ZWx5XHJcbiAgICAgIGNvbnN0IHVzZXJNZXNzYWdlOiBNZXNzYWdlID0ge1xyXG4gICAgICAgIGlkOiB0ZW1wSWQsXHJcbiAgICAgICAgcm9sZTogJ3VzZXInLFxyXG4gICAgICAgIGNvbnRlbnQ6IG1lc3NhZ2VUZXh0LFxyXG4gICAgICAgIGNvbnRlbnRUeXBlOiAndGV4dCcsXHJcbiAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgIH07XHJcblxyXG4gICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IFsuLi5wcmV2LCB1c2VyTWVzc2FnZV0pO1xyXG5cclxuICAgICAgY29uc29sZS5sb2coJ1NlbmRpbmcgbWVzc2FnZSB0byBjaGF0OicsIGNoYXQuaWQsIHsgbWVzc2FnZTogbWVzc2FnZVRleHQsIHN0cmVhbWluZzogdHJ1ZSB9KTtcclxuXHJcbiAgICAgIC8vIFNlbmQgbWVzc2FnZSB3aXRoIHN0cmVhbWluZyBlbmFibGVkXHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgY2hhdFNlcnZpY2Uuc2VuZE1lc3NhZ2UoY2hhdC5pZCwge1xyXG4gICAgICAgIG1lc3NhZ2U6IG1lc3NhZ2VUZXh0LFxyXG4gICAgICAgIHN0cmVhbWluZzogdHJ1ZSxcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBjb25zb2xlLmxvZygnTWVzc2FnZSBzZW50IHN1Y2Nlc3NmdWxseTonLCByZXNwb25zZSk7XHJcblxyXG4gICAgICAvLyBVcGRhdGUgdGhlIHRlbXBvcmFyeSBtZXNzYWdlIHdpdGggcmVhbCBJRCBpZiBhdmFpbGFibGVcclxuICAgICAgaWYgKHJlc3BvbnNlLmlkKSB7XHJcbiAgICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiBwcmV2Lm1hcChtc2cgPT5cclxuICAgICAgICAgIG1zZy5pZCA9PT0gdGVtcElkID8geyAuLi5tc2csIGlkOiByZXNwb25zZS5pZCB9IDogbXNnXHJcbiAgICAgICAgKSk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFN0YXJ0IHN0cmVhbWluZyByZXNwb25zZVxyXG4gICAgICBhd2FpdCBzdGFydFN0cmVhbWluZygpO1xyXG5cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBzZW5kIG1lc3NhZ2U6JywgZXJyb3IpO1xyXG4gICAgICAvLyBSZW1vdmUgdGhlIHRlbXBvcmFyeSB1c2VyIG1lc3NhZ2Ugb24gZXJyb3JcclxuICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiBwcmV2LmZpbHRlcihtc2cgPT4gbXNnLmlkICE9PSB0ZW1wSWQpKTtcclxuICAgICAgYWxlcnQoJ0ZhaWxlZCB0byBzZW5kIG1lc3NhZ2UuIFBsZWFzZSB0cnkgYWdhaW4uJyk7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRTZW5kaW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9LCBbc2VuZGluZywgY2hhdC5pZF0pO1xyXG5cclxuICBjb25zdCBzdGFydFN0cmVhbWluZyA9IGFzeW5jICgpID0+IHtcclxuICAgIGNvbnNvbGUubG9nKCdTdGFydGluZyBzdHJlYW0gZm9yIGNoYXQ6JywgY2hhdC5pZCk7XHJcbiAgICBzZXRJc1N0cmVhbWluZyh0cnVlKTtcclxuICAgIHNldFN0cmVhbWluZ01lc3NhZ2UoJycpO1xyXG5cclxuICAgIC8vIENsb3NlIGV4aXN0aW5nIGNvbm5lY3Rpb24gYW5kIGNsZWFyIHRpbWVvdXRzXHJcbiAgICBpZiAoc3RyZWFtQ29ubmVjdGlvblJlZi5jdXJyZW50KSB7XHJcbiAgICAgIHN0cmVhbUNvbm5lY3Rpb25SZWYuY3VycmVudC5jbG9zZSgpO1xyXG4gICAgfVxyXG4gICAgaWYgKHN0cmVhbWluZ1RpbWVvdXRSZWYuY3VycmVudCkge1xyXG4gICAgICBjbGVhclRpbWVvdXQoc3RyZWFtaW5nVGltZW91dFJlZi5jdXJyZW50KTtcclxuICAgIH1cclxuICAgIGlmIChzdHJlYW1UaW1lb3V0UmVmLmN1cnJlbnQpIHtcclxuICAgICAgY2xlYXJUaW1lb3V0KHN0cmVhbVRpbWVvdXRSZWYuY3VycmVudCk7XHJcbiAgICB9XHJcblxyXG4gICAgbGV0IGN1cnJlbnRTdHJlYW1pbmdNZXNzYWdlID0gJyc7XHJcblxyXG4gICAgLy8gTW9yZSBhZ2dyZXNzaXZlIHRocm90dGxpbmcgZm9yIHN0cmVhbWluZyBtZXNzYWdlIHVwZGF0ZXNcclxuICAgIGxldCBsYXN0VXBkYXRlVGltZSA9IDA7XHJcbiAgICBjb25zdCB1cGRhdGVTdHJlYW1pbmdNZXNzYWdlID0gKG1lc3NhZ2U6IHN0cmluZykgPT4ge1xyXG4gICAgICBjb25zdCBub3cgPSBEYXRlLm5vdygpO1xyXG4gICAgICBpZiAoc3RyZWFtaW5nVGltZW91dFJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgY2xlYXJUaW1lb3V0KHN0cmVhbWluZ1RpbWVvdXRSZWYuY3VycmVudCk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIEltbWVkaWF0ZSB1cGRhdGUgaWYgaXQncyBiZWVuIG1vcmUgdGhhbiAxMDBtcyBzaW5jZSBsYXN0IHVwZGF0ZVxyXG4gICAgICBpZiAobm93IC0gbGFzdFVwZGF0ZVRpbWUgPiAxMDApIHtcclxuICAgICAgICBzZXRTdHJlYW1pbmdNZXNzYWdlKG1lc3NhZ2UpO1xyXG4gICAgICAgIGxhc3RVcGRhdGVUaW1lID0gbm93O1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIC8vIE90aGVyd2lzZSwgdGhyb3R0bGUgdGhlIHVwZGF0ZVxyXG4gICAgICAgIHN0cmVhbWluZ1RpbWVvdXRSZWYuY3VycmVudCA9IHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgc2V0U3RyZWFtaW5nTWVzc2FnZShtZXNzYWdlKTtcclxuICAgICAgICAgIGxhc3RVcGRhdGVUaW1lID0gRGF0ZS5ub3coKTtcclxuICAgICAgICB9LCAxMDApO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIGNvbnN0IG9uTWVzc2FnZSA9IChkYXRhOiBTU0VFdmVudCkgPT4ge1xyXG4gICAgICBjb25zb2xlLmxvZygnUmVjZWl2ZWQgU1NFIGV2ZW50OicsIGRhdGEpO1xyXG5cclxuICAgICAgc3dpdGNoIChkYXRhLmV2ZW50KSB7XHJcbiAgICAgICAgY2FzZSAnc3RhcnQnOlxyXG4gICAgICAgICAgY29uc29sZS5sb2coJ1N0cmVhbSBzdGFydGVkJyk7XHJcbiAgICAgICAgICBjdXJyZW50U3RyZWFtaW5nTWVzc2FnZSA9ICcnO1xyXG4gICAgICAgICAgc2V0U3RyZWFtaW5nTWVzc2FnZSgnJyk7XHJcbiAgICAgICAgICBicmVhaztcclxuICAgICAgICBjYXNlICd0b2tlbic6XHJcbiAgICAgICAgICBjdXJyZW50U3RyZWFtaW5nTWVzc2FnZSArPSBkYXRhLmRhdGE7XHJcbiAgICAgICAgICB1cGRhdGVTdHJlYW1pbmdNZXNzYWdlKGN1cnJlbnRTdHJlYW1pbmdNZXNzYWdlKTtcclxuICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgIGNhc2UgJ21ldGFkYXRhJzpcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdTdHJlYW0gbWV0YWRhdGE6JywgZGF0YS5kYXRhKTtcclxuICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgIGNhc2UgJ2VuZCc6XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnU3RyZWFtIGVuZGVkLCBmaW5hbCBtZXNzYWdlOicsIGN1cnJlbnRTdHJlYW1pbmdNZXNzYWdlKTtcclxuICAgICAgICAgIC8vIENsZWFyIGFueSBwZW5kaW5nIHN0cmVhbWluZyB1cGRhdGVzXHJcbiAgICAgICAgICBpZiAoc3RyZWFtaW5nVGltZW91dFJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgICAgIGNsZWFyVGltZW91dChzdHJlYW1pbmdUaW1lb3V0UmVmLmN1cnJlbnQpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgLy8gQ2xlYXIgc3RyZWFtIHRpbWVvdXQgdG8gcHJldmVudCBmYWxzZSB0aW1lb3V0IGVycm9yc1xyXG4gICAgICAgICAgaWYgKHN0cmVhbVRpbWVvdXRSZWYuY3VycmVudCkge1xyXG4gICAgICAgICAgICBjbGVhclRpbWVvdXQoc3RyZWFtVGltZW91dFJlZi5jdXJyZW50KTtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIC8vIEZpbmFsaXplIHRoZSBzdHJlYW1pbmcgbWVzc2FnZVxyXG4gICAgICAgICAgY29uc3QgZmluYWxNZXNzYWdlOiBNZXNzYWdlID0ge1xyXG4gICAgICAgICAgICBpZDogZGF0YS5kYXRhPy5jaGF0TWVzc2FnZUlkIHx8IGBtc2ctJHtEYXRlLm5vdygpfWAsXHJcbiAgICAgICAgICAgIHJvbGU6ICdhc3Npc3RhbnQnLFxyXG4gICAgICAgICAgICBjb250ZW50OiBjdXJyZW50U3RyZWFtaW5nTWVzc2FnZSxcclxuICAgICAgICAgICAgY29udGVudFR5cGU6ICd0ZXh0JyxcclxuICAgICAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxyXG4gICAgICAgICAgfTtcclxuXHJcbiAgICAgICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IFsuLi5wcmV2LCBmaW5hbE1lc3NhZ2VdKTtcclxuICAgICAgICAgIHNldFN0cmVhbWluZ01lc3NhZ2UoJycpO1xyXG4gICAgICAgICAgc2V0SXNTdHJlYW1pbmcoZmFsc2UpO1xyXG4gICAgICAgICAgaWYgKHN0cmVhbUNvbm5lY3Rpb25SZWYuY3VycmVudCkge1xyXG4gICAgICAgICAgICBzdHJlYW1Db25uZWN0aW9uUmVmLmN1cnJlbnQuY2xvc2UoKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnVW5rbm93biBTU0UgZXZlbnQ6JywgZGF0YS5ldmVudCk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgY29uc3Qgb25FcnJvciA9IGFzeW5jIChlcnJvcjogYW55KSA9PiB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1N0cmVhbSBFcnJvcjonLCBlcnJvcik7XHJcblxyXG4gICAgICAvLyBDbGVhciB0aW1lb3V0cyBmaXJzdCB0byBwcmV2ZW50IGZ1cnRoZXIgZXJyb3JzXHJcbiAgICAgIGlmIChzdHJlYW1pbmdUaW1lb3V0UmVmLmN1cnJlbnQpIHtcclxuICAgICAgICBjbGVhclRpbWVvdXQoc3RyZWFtaW5nVGltZW91dFJlZi5jdXJyZW50KTtcclxuICAgICAgfVxyXG4gICAgICBpZiAoc3RyZWFtVGltZW91dFJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgY2xlYXJUaW1lb3V0KHN0cmVhbVRpbWVvdXRSZWYuY3VycmVudCk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHNldElzU3RyZWFtaW5nKGZhbHNlKTtcclxuICAgICAgc2V0U3RyZWFtaW5nTWVzc2FnZSgnJyk7XHJcbiAgICAgIGlmIChzdHJlYW1Db25uZWN0aW9uUmVmLmN1cnJlbnQpIHtcclxuICAgICAgICBzdHJlYW1Db25uZWN0aW9uUmVmLmN1cnJlbnQuY2xvc2UoKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gT25seSByZWxvYWQgbWVzc2FnZXMgaWYgaXQncyBub3QgYSB0aW1lb3V0IGVycm9yIGFuZCBzdHJlYW1pbmcgd2FzIGFjdHVhbGx5IGFjdGl2ZVxyXG4gICAgICBpZiAoIWVycm9yLm1lc3NhZ2U/LmluY2x1ZGVzKCd0aW1lb3V0JykgJiYgaXNTdHJlYW1pbmcpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnQXR0ZW1wdGluZyB0byByZWxvYWQgbWVzc2FnZXMgYXMgZmFsbGJhY2suLi4nKTtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgYXdhaXQgbG9hZE1lc3NhZ2VzKCk7XHJcbiAgICAgICAgfSBjYXRjaCAocmVsb2FkRXJyb3IpIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byByZWxvYWQgbWVzc2FnZXM6JywgcmVsb2FkRXJyb3IpO1xyXG4gICAgICAgICAgLy8gRG9uJ3Qgc2hvdyBhbGVydCBmb3IgdGltZW91dCBlcnJvcnMgdG8gYXZvaWQgZGlzcnVwdGluZyB1c2VyXHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIENyZWF0ZSBuZXcgc3RyZWFtIGNvbm5lY3Rpb25cclxuICAgICAgY29uc3QgY29ubmVjdGlvbiA9IGF3YWl0IGNoYXRTZXJ2aWNlLmNyZWF0ZVN0cmVhbUNvbm5lY3Rpb24oY2hhdC5pZCwgb25NZXNzYWdlLCBvbkVycm9yKTtcclxuICAgICAgc3RyZWFtQ29ubmVjdGlvblJlZi5jdXJyZW50ID0gY29ubmVjdGlvbjtcclxuXHJcbiAgICAgIC8vIFNldCB0aW1lb3V0IGZvciBzdHJlYW1pbmcgKDMwIHNlY29uZHMpIC0gYnV0IHN0b3JlIHJlZmVyZW5jZSB0byBjbGVhciBpdFxyXG4gICAgICBzdHJlYW1UaW1lb3V0UmVmLmN1cnJlbnQgPSBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAvLyBPbmx5IHRyaWdnZXIgdGltZW91dCBpZiBzdHJlYW1pbmcgaXMgc3RpbGwgYWN0aXZlIGFuZCBjb25uZWN0aW9uIGV4aXN0c1xyXG4gICAgICAgIGlmIChpc1N0cmVhbWluZyAmJiBzdHJlYW1Db25uZWN0aW9uUmVmLmN1cnJlbnQpIHtcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdTdHJlYW0gdGltZW91dCwgZmFsbGluZyBiYWNrIHRvIG1lc3NhZ2UgcmVsb2FkJyk7XHJcbiAgICAgICAgICBvbkVycm9yKG5ldyBFcnJvcignU3RyZWFtIHRpbWVvdXQnKSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9LCAzMDAwMCk7XHJcblxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGNyZWF0ZSBzdHJlYW0gY29ubmVjdGlvbjonLCBlcnJvcik7XHJcbiAgICAgIHNldElzU3RyZWFtaW5nKGZhbHNlKTtcclxuICAgICAgYWxlcnQoJ0ZhaWxlZCB0byBlc3RhYmxpc2ggY29ubmVjdGlvbiBmb3IgQUkgcmVzcG9uc2UuIFBsZWFzZSB0cnkgYWdhaW4uJyk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgaWYgKGxvYWRpbmcpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBoLWZ1bGwgb3ZlcmZsb3ctaGlkZGVuXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wIGgtMjAgYm9yZGVyLWIgcC00IGFuaW1hdGUtcHVsc2UgZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMCBoLTEwIGJnLW11dGVkIHJvdW5kZWQtZnVsbFwiIC8+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTQgYmctbXV0ZWQgcm91bmRlZCB3LTMyXCIgLz5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMyBiZy1tdXRlZCByb3VuZGVkIHctMjBcIiAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHAtNCBzcGFjZS15LTQgb3ZlcmZsb3cteS1hdXRvXCI+XHJcbiAgICAgICAgICB7QXJyYXkuZnJvbSh7IGxlbmd0aDogMyB9KS5tYXAoKF8sIGkpID0+IChcclxuICAgICAgICAgICAgPGRpdiBrZXk9e2l9IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0zIGFuaW1hdGUtcHVsc2VcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctbXV0ZWQgcm91bmRlZC1mdWxsXCIgLz5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLW11dGVkIHJvdW5kZWQgdy0zLzRcIiAvPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTQgYmctbXV0ZWQgcm91bmRlZCB3LTEvMlwiIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgKSl9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaC1mdWxsIG92ZXJmbG93LWhpZGRlblwiPlxyXG4gICAgICB7LyogQ2hhdCBIZWFkZXIgLSBGaXhlZCAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wIGgtMjAgYm9yZGVyLWIgcC00IGJnLWJhY2tncm91bmQvOTUgYmFja2Ryb3AtYmx1ciBzdXBwb3J0cy1bYmFja2Ryb3AtZmlsdGVyXTpiZy1iYWNrZ3JvdW5kLzYwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiB3LWZ1bGxcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxyXG4gICAgICAgICAgey8qIE1vYmlsZSBiYWNrIGJ1dHRvbiAqL31cclxuICAgICAgICAgIHtpc01vYmlsZSAmJiBvbkJhY2sgJiYgKFxyXG4gICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcclxuICAgICAgICAgICAgICBzaXplPVwiaWNvblwiXHJcbiAgICAgICAgICAgICAgb25DbGljaz17b25CYWNrfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImhvdmVyOmJnLW11dGVkLzUwIG1kOmhpZGRlblwiXHJcbiAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIkJhY2sgdG8gY2hhdCBsaXN0XCJcclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIDxBcnJvd0xlZnQgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XHJcbiAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyAke1xyXG4gICAgICAgICAgICAgIGlzTW9iaWxlICYmIG9uVG9nZ2xlUHJvZmlsZSA/ICdjdXJzb3ItcG9pbnRlciBob3ZlcjpiZy1tdXRlZC81MCByb3VuZGVkLWxnIHAtMiAtbS0yIHRyYW5zaXRpb24tY29sb3JzJyA6ICcnXHJcbiAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICBvbkNsaWNrPXtpc01vYmlsZSAmJiBvblRvZ2dsZVByb2ZpbGUgPyBvblRvZ2dsZVByb2ZpbGUgOiB1bmRlZmluZWR9XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cclxuICAgICAgICAgICAgICA8QXZhdGFyIGNsYXNzTmFtZT1cInctMTIgaC0xMiByaW5nLTIgcmluZy1iZXN0aWVrdS1wcmltYXJ5LzIwXCI+XHJcbiAgICAgICAgICAgICAgICA8QXZhdGFySW1hZ2Ugc3JjPXtjaGF0LmNoYXJhY3Rlcj8uaW1hZ2V9IGFsdD17Y2hhdC5jaGFyYWN0ZXI/Lm5hbWV9IC8+XHJcbiAgICAgICAgICAgICAgICA8QXZhdGFyRmFsbGJhY2sgY2xhc3NOYW1lPVwiYmctYmVzdGlla3UtcHJpbWFyeS8xMCB0ZXh0LWJlc3RpZWt1LXByaW1hcnkgZm9udC1zZW1pYm9sZFwiPlxyXG4gICAgICAgICAgICAgICAgICB7Y2hhdC5jaGFyYWN0ZXI/Lm5hbWU/LmNoYXJBdCgwKT8udG9VcHBlckNhc2UoKSB8fCAnQyd9XHJcbiAgICAgICAgICAgICAgICA8L0F2YXRhckZhbGxiYWNrPlxyXG4gICAgICAgICAgICAgIDwvQXZhdGFyPlxyXG4gICAgICAgICAgICAgIHsvKiBPbmxpbmUgaW5kaWNhdG9yICovfVxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTAgcmlnaHQtMCB3LTMgaC0zIGJnLWdyZWVuLTUwMCBib3JkZXItMiBib3JkZXItYmFja2dyb3VuZCByb3VuZGVkLWZ1bGxcIj48L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1sZ1wiPntjaGF0LmNoYXJhY3Rlcj8ubmFtZSB8fCAnVW5rbm93biBDaGFyYWN0ZXInfTwvaDI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiB0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxyXG4gICAgICAgICAgICAgICAgPHNwYW4+e2NoYXQubWVzc2FnZUNvdW50fSBwZXNhbjwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDxzcGFuPuKAojwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNTAwXCI+T25saW5lPC9zcGFuPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICB7LyogTW9iaWxlIHByb2ZpbGUgdG9nZ2xlIGJ1dHRvbiAqL31cclxuICAgICAgICAgIHtpc01vYmlsZSAmJiBvblRvZ2dsZVByb2ZpbGUgJiYgKFxyXG4gICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcclxuICAgICAgICAgICAgICBzaXplPVwiaWNvblwiXHJcbiAgICAgICAgICAgICAgb25DbGljaz17b25Ub2dnbGVQcm9maWxlfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImhvdmVyOmJnLW11dGVkLzUwIG1kOmhpZGRlblwiXHJcbiAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIlZpZXcgY2hhcmFjdGVyIHByb2ZpbGVcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPEluZm8gY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XHJcbiAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICB7LyogUmVzZXQgQ2hhdCBCdXR0b24gKi99XHJcbiAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXHJcbiAgICAgICAgICAgIHNpemU9XCJzbVwiXHJcbiAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZURlbGV0ZU1lc3NhZ2VzfVxyXG4gICAgICAgICAgICBkaXNhYmxlZD17aXNEZWxldGluZyB8fCBtZXNzYWdlcy5sZW5ndGggPT09IDB9XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCBob3Zlcjp0ZXh0LXJlZC03MDAgaG92ZXI6YmctcmVkLTUwIGRhcms6aG92ZXI6YmctcmVkLTk1MCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiXHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIDxSb3RhdGVDY3cgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XHJcbiAgICAgICAgICAgIHtpc0RlbGV0aW5nID8gJ01lcmVzZXQuLi4nIDogJ1Jlc2V0J31cclxuICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIHsvKiBQZXNhbiAtIFNjcm9sbGFibGUgKi99XHJcbiAgICAgIDxkaXZcclxuICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgb3ZlcmZsb3cteS1hdXRvIG92ZXJmbG93LXgtaGlkZGVuIHAtNCBzcGFjZS15LTQgcmVsYXRpdmVcIlxyXG4gICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiBiYWNrZ3JvdW5kQXNzZXRVcmxcclxuICAgICAgICAgICAgPyBgbGluZWFyLWdyYWRpZW50KHJnYmEoMCwgMCwgMCwgMC42KSwgcmdiYSgwLCAwLCAwLCAwLjgpKSwgdXJsKCR7YmFja2dyb3VuZEFzc2V0VXJsfSlgXHJcbiAgICAgICAgICAgIDogJ2xpbmVhci1ncmFkaWVudCh0byBib3R0b20sIGhzbCh2YXIoLS1iYWNrZ3JvdW5kKSksIGhzbCh2YXIoLS1tdXRlZCkpLzAuMiknLFxyXG4gICAgICAgICAgYmFja2dyb3VuZFNpemU6IGJhY2tncm91bmRBc3NldFVybCA/ICdjb3ZlcicgOiAnYXV0bycsXHJcbiAgICAgICAgICBiYWNrZ3JvdW5kUG9zaXRpb246IGJhY2tncm91bmRBc3NldFVybCA/ICdjZW50ZXInIDogJ2F1dG8nLFxyXG4gICAgICAgICAgYmFja2dyb3VuZFJlcGVhdDogYmFja2dyb3VuZEFzc2V0VXJsID8gJ25vLXJlcGVhdCcgOiAnYXV0bydcclxuICAgICAgICB9fVxyXG4gICAgICA+XHJcbiAgICAgICAge21lc3NhZ2VzLm1hcCgobWVzc2FnZSkgPT4gKFxyXG4gICAgICAgICAgPE1lc3NhZ2VJdGVtXHJcbiAgICAgICAgICAgIGtleT17bWVzc2FnZS5pZH1cclxuICAgICAgICAgICAgbWVzc2FnZT17bWVzc2FnZX1cclxuICAgICAgICAgICAgY2hhcmFjdGVySW1hZ2U9e2NoYXJhY3RlckltYWdlUmVmLmN1cnJlbnR9XHJcbiAgICAgICAgICAgIGNoYXJhY3Rlck5hbWU9e2NoYXJhY3Rlck5hbWVSZWYuY3VycmVudH1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgKSl9XHJcblxyXG4gICAgICAgIHsvKiBTdHJlYW1pbmcgTWVzc2FnZSAqL31cclxuICAgICAgICB7aXNTdHJlYW1pbmcgJiYgKFxyXG4gICAgICAgICAgPFN0cmVhbWluZ01lc3NhZ2VcclxuICAgICAgICAgICAgc3RyZWFtaW5nTWVzc2FnZT17c3RyZWFtaW5nTWVzc2FnZX1cclxuICAgICAgICAgICAgY2hhcmFjdGVySW1hZ2U9e2NoYXJhY3RlckltYWdlUmVmLmN1cnJlbnR9XHJcbiAgICAgICAgICAgIGNoYXJhY3Rlck5hbWU9e2NoYXJhY3Rlck5hbWVSZWYuY3VycmVudH1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgKX1cclxuXHJcbiAgICAgICAgPGRpdiByZWY9e21lc3NhZ2VzRW5kUmVmfSAvPlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIHsvKiBNZXNzYWdlIElucHV0IC0gRml4ZWQgKi99XHJcbiAgICAgIDxDaGF0SW5wdXRcclxuICAgICAgICBvblNlbmRNZXNzYWdlPXtoYW5kbGVTZW5kTWVzc2FnZX1cclxuICAgICAgICBkaXNhYmxlZD17c2VuZGluZyB8fCBpc1N0cmVhbWluZ31cclxuICAgICAgICBjaGFyYWN0ZXJOYW1lPXtjaGFyYWN0ZXJOYW1lUmVmLmN1cnJlbnR9XHJcbiAgICAgICAgaXNTdHJlYW1pbmc9e2lzU3RyZWFtaW5nfVxyXG4gICAgICAvPlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufSk7XHJcblxyXG5DaGF0SW50ZXJmYWNlLmRpc3BsYXlOYW1lID0gJ0NoYXRJbnRlcmZhY2UnO1xyXG5cclxuZXhwb3J0IHsgQ2hhdEludGVyZmFjZSB9O1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZUNhbGxiYWNrIiwibWVtbyIsImNoYXRTZXJ2aWNlIiwiY2hhcmFjdGVyU2VydmljZSIsIkJ1dHRvbiIsIklucHV0IiwiQXZhdGFyIiwiQXZhdGFyRmFsbGJhY2siLCJBdmF0YXJJbWFnZSIsIlNlbmQiLCJBcnJvd0xlZnQiLCJJbmZvIiwiUm90YXRlQ2N3IiwiZm9ybWF0RGlzdGFuY2VUb05vdyIsImlkIiwiUmVhY3RNYXJrZG93biIsInJlbWFya0dmbSIsInRvYXN0IiwiTWVzc2FnZUNvbnRlbnQiLCJjb250ZW50Iiwicm9sZSIsInAiLCJjbGFzc05hbWUiLCJkaXYiLCJyZW1hcmtQbHVnaW5zIiwiY29tcG9uZW50cyIsImNoaWxkcmVuIiwiZW0iLCJzdHJvbmciLCJjb2RlIiwiaXNJbmxpbmUiLCJ1bCIsIm9sIiwibGkiLCJibG9ja3F1b3RlIiwiTWVzc2FnZUl0ZW0iLCJtZXNzYWdlIiwiY2hhcmFjdGVySW1hZ2UiLCJjaGFyYWN0ZXJOYW1lIiwiZm9ybWF0VGltZSIsImRhdGVTdHJpbmciLCJEYXRlIiwiYWRkU3VmZml4IiwibG9jYWxlIiwic3JjIiwiYWx0IiwiY2hhckF0IiwidG9VcHBlckNhc2UiLCJjcmVhdGVkQXQiLCJTdHJlYW1pbmdNZXNzYWdlIiwic3RyZWFtaW5nTWVzc2FnZSIsInNwYW4iLCJzdHlsZSIsImFuaW1hdGlvbkRlbGF5IiwiQ2hhdElucHV0Iiwib25TZW5kTWVzc2FnZSIsImRpc2FibGVkIiwiaXNTdHJlYW1pbmciLCJuZXdNZXNzYWdlIiwic2V0TmV3TWVzc2FnZSIsImlucHV0UmVmIiwibWVzc2FnZVJlZiIsImN1cnJlbnQiLCJoYW5kbGVTZW5kTWVzc2FnZSIsImN1cnJlbnRNZXNzYWdlIiwidHJpbSIsImhhbmRsZUtleVByZXNzIiwiZSIsImtleSIsInNoaWZ0S2V5IiwicHJldmVudERlZmF1bHQiLCJoYW5kbGVJbnB1dENoYW5nZSIsInZhbHVlIiwidGFyZ2V0IiwicmVmIiwib25DaGFuZ2UiLCJvbktleVByZXNzIiwicGxhY2Vob2xkZXIiLCJvbkNsaWNrIiwiQ2hhdEludGVyZmFjZSIsImNoYXQiLCJvbkJhY2siLCJvblRvZ2dsZVByb2ZpbGUiLCJvbkNoYXRSZXNldCIsInNlbGVjdGVkQmFja2dyb3VuZCIsIm1lc3NhZ2VzIiwic2V0TWVzc2FnZXMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInNlbmRpbmciLCJzZXRTZW5kaW5nIiwic2V0U3RyZWFtaW5nTWVzc2FnZSIsInNldElzU3RyZWFtaW5nIiwiaXNNb2JpbGUiLCJzZXRJc01vYmlsZSIsImNoYXJhY3RlckFzc2V0cyIsInNldENoYXJhY3RlckFzc2V0cyIsImJhY2tncm91bmRBc3NldFVybCIsInNldEJhY2tncm91bmRBc3NldFVybCIsImlzRGVsZXRpbmciLCJzZXRJc0RlbGV0aW5nIiwiY2hhcmFjdGVyIiwic2V0Q2hhcmFjdGVyIiwibWVzc2FnZXNFbmRSZWYiLCJzdHJlYW1Db25uZWN0aW9uUmVmIiwic3RyZWFtaW5nVGltZW91dFJlZiIsInN0cmVhbVRpbWVvdXRSZWYiLCJoYW5kbGVEZWxldGVNZXNzYWdlcyIsImRlbGV0ZUNoYXRNZXNzYWdlcyIsInN1Y2Nlc3MiLCJkZXNjcmlwdGlvbiIsImR1cmF0aW9uIiwiZXJyb3IiLCJjb25zb2xlIiwiY2hhcmFjdGVySW1hZ2VSZWYiLCJpbWFnZSIsImNoYXJhY3Rlck5hbWVSZWYiLCJuYW1lIiwibG9hZENoYXJhY3RlckRhdGEiLCJjaGFyYWN0ZXJEYXRhIiwiZ2V0Q2hhcmFjdGVyQnlJZCIsImNoYXJhY3RlcklkIiwiYXNzZXRzIiwiZ2V0Q2hhcmFjdGVyQXNzZXRzIiwibGVuZ3RoIiwiYmFja2dyb3VuZEFzc2V0IiwiZmluZCIsImFzc2V0IiwicHVycG9zZSIsInVybCIsImNoZWNrTW9iaWxlIiwid2luZG93IiwiaW5uZXJXaWR0aCIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwibG9hZE1lc3NhZ2VzIiwiY2xvc2UiLCJjbGVhclRpbWVvdXQiLCJzY3JvbGxUb0JvdHRvbVJlZiIsInNjcm9sbEludG9WaWV3IiwiYmVoYXZpb3IiLCJtZXNzYWdlc0NvdW50UmVmIiwidGltZW91dElkIiwic2V0VGltZW91dCIsImlzU3RyZWFtaW5nUmVmIiwicmVzcG9uc2UiLCJnZXRDaGF0TWVzc2FnZXMiLCJsaW1pdCIsImRhdGEiLCJyZXZlcnNlIiwibWVzc2FnZVRleHQiLCJ0ZW1wSWQiLCJub3ciLCJ1c2VyTWVzc2FnZSIsImNvbnRlbnRUeXBlIiwidG9JU09TdHJpbmciLCJ1cGRhdGVkQXQiLCJwcmV2IiwibG9nIiwic3RyZWFtaW5nIiwic2VuZE1lc3NhZ2UiLCJtYXAiLCJtc2ciLCJzdGFydFN0cmVhbWluZyIsImZpbHRlciIsImFsZXJ0IiwiY3VycmVudFN0cmVhbWluZ01lc3NhZ2UiLCJsYXN0VXBkYXRlVGltZSIsInVwZGF0ZVN0cmVhbWluZ01lc3NhZ2UiLCJvbk1lc3NhZ2UiLCJldmVudCIsImZpbmFsTWVzc2FnZSIsImNoYXRNZXNzYWdlSWQiLCJvbkVycm9yIiwiaW5jbHVkZXMiLCJyZWxvYWRFcnJvciIsImNvbm5lY3Rpb24iLCJjcmVhdGVTdHJlYW1Db25uZWN0aW9uIiwiRXJyb3IiLCJBcnJheSIsImZyb20iLCJfIiwiaSIsInZhcmlhbnQiLCJzaXplIiwiYXJpYS1sYWJlbCIsInVuZGVmaW5lZCIsImgyIiwibWVzc2FnZUNvdW50IiwiYmFja2dyb3VuZCIsImJhY2tncm91bmRTaXplIiwiYmFja2dyb3VuZFBvc2l0aW9uIiwiYmFja2dyb3VuZFJlcGVhdCIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/chat-interface.tsx\n"));

/***/ })

});