import { useState, useEffect } from 'react';
import { CreditBalance } from '@/types/credit';
import { creditService } from '@/services/credit';
import { Button } from '@/components/ui/button';
import { Coins, RefreshCw, Plus, ShoppingCart } from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';

export function CreditBalanceWidget() {
  const { isAuthenticated } = useAuth();
  const [balance, setBalance] = useState<CreditBalance | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isAuthenticated) {
      loadBalance();
    } else {
      setBalance(null);
    }
  }, [isAuthenticated]);

  const loadBalance = async () => {
    try {
      setLoading(true);
      const balanceData = await creditService.getCreditBalance();
      setBalance(balanceData);
    } catch (error) {
      console.error('Failed to load credit balance:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="space-y-2">
        {/* Credit Info */}
        <div className="p-3 bg-gradient-to-r from-bestieku-primary/10 to-bestieku-primary/5 rounded-xl border border-bestieku-primary/20">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-bestieku-primary/20 rounded-lg flex items-center justify-center">
              <Coins className="w-5 h-5 text-bestieku-primary" />
            </div>
            <div className="flex-1">
              <div className="font-semibold text-sm">Credits</div>
              <div className="text-xs text-muted-foreground">Masuk untuk melihat saldo</div>
            </div>
          </div>
        </div>

        {/* Buy Credits Button */}
        <Button
          onClick={() => window.location.href = '/credits'}
          className="w-full bg-bestieku-primary hover:bg-bestieku-primary-dark font-semibold h-9 text-sm"
        >
          <ShoppingCart className="w-4 h-4 mr-2" />
          Beli B Coin
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {/* Credit Balance */}
      <div className="p-3 bg-gradient-to-r from-bestieku-primary/10 to-bestieku-primary/5 rounded-xl border border-bestieku-primary/20">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-bestieku-primary/20 rounded-lg flex items-center justify-center">
            <Coins className="w-5 h-5 text-bestieku-primary" />
          </div>
          <div className="flex-1">
            <div className="font-semibold text-sm">B Coin</div>
            {loading ? (
              <div className="h-4 bg-muted rounded animate-pulse w-16"></div>
            ) : balance ? (
              <div className="text-xs text-bestieku-primary font-medium">
                {balance.balance.toLocaleString('id-ID')} tersedia
              </div>
            ) : (
              <div className="text-xs text-red-600">Gagal memuat</div>
            )}
          </div>
          <Button
            onClick={loadBalance}
            variant="ghost"
            size="icon"
            className="h-8 w-8 hover:bg-bestieku-primary/10"
            disabled={loading}
          >
            <RefreshCw className={`w-4 h-4 text-bestieku-primary ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* Buy Credits Button */}
      <Button
        onClick={() => window.location.href = '/credits'}
        className="w-full bg-bestieku-primary hover:bg-bestieku-primary-dark font-semibold h-9 text-sm"
      >
        <Plus className="w-4 h-4 mr-2" />
        Beli B Coin
      </Button>
    </div>
  );
}
