'use client';

import React from 'react';
import { Character } from '@/types/character';
import { Badge } from '@/components/ui/badge';
import { MessageCircle, Users } from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';

interface CharacterCardProps {
  character: Character;
  onStartChat?: (characterId: string) => void;
}

export function CharacterCard({ character, onStartChat }: CharacterCardProps) {
  const { isAuthenticated } = useAuth();

  const handleCardClick = () => {
    if (isAuthenticated && onStartChat) {
      onStartChat(character.id);
    }
  };

  return (
    <div
      className={`bg-card border rounded-xl p-4 transition-all duration-200 group ${
        isAuthenticated
          ? 'hover:shadow-lg hover:scale-[1.02] cursor-pointer hover:border-bestieku-primary/50'
          : 'hover:shadow-md cursor-default opacity-90'
      }`}
      onClick={handleCardClick}
    >
      {/* Character Image */}
      <div className="relative mb-3">
        <div className="bg-muted/50 aspect-square rounded-lg overflow-hidden">
          {character.image ? (
            <img
              src={character.image}
              alt={character.name}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-muted-foreground">
              <MessageCircle className="w-12 h-12" />
            </div>
          )}
        </div>
        
        {/* Story Mode Badge */}
        {character.storyMode && (
          <Badge
            variant="secondary"
            className="absolute top-2 right-2 bg-white text-black border border-gray-200"
          >
            Story Mode
          </Badge>
        )}

        {/* Popularity Badge */}
        {character.messageCount > 0 && (
          <Badge
            variant="secondary"
            className="absolute top-2 left-2 bg-black/70 text-white text-xs flex items-center gap-1"
          >
            <Users className="w-3 h-3" />
            {character.messageCount > 999 ? '999+' : character.messageCount}
          </Badge>
        )}
      </div>

      {/* Character Info */}
      <div className="space-y-2">
        <h3 className="font-semibold text-lg leading-tight">{character.name}</h3>
        
        <p className="text-sm text-muted-foreground line-clamp-2 min-h-[2.5rem]">
          {character.description}
        </p>

        {/* Tags */}
        {character.tags && character.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {character.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {character.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{character.tags.length - 3}
              </Badge>
            )}
          </div>
        )}

        {/* Status Indicator */}
        <div className="pt-2 flex items-center justify-between">
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <MessageCircle className="w-3 h-3" />
            <span>{isAuthenticated ? 'Klik untuk memulai percakapan' : 'Login/Signup untuk memulai percakapan'}</span>
          </div>
          {isAuthenticated && (
            <div className="flex items-center gap-1 text-bestieku-primary opacity-0 group-hover:opacity-100 transition-opacity">
              <span className="text-xs font-medium">Mulai →</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
